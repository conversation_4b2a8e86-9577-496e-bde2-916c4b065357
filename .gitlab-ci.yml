# GitLab CI/CD Pipeline Configuration for Message Center SDK
# Automatically runs linter, tests, and publishes the library
stages:
  - install
  - lint
  - test
  - build
  - publish

variables:
  NPM_CONFIG_REGISTRY: https://artifactory.cps-artifacts.metro-markets.org/artifactory/api/npm/npm-virtual/
  NPM_CONFIG_ALWAYS_AUTH: 'true'

# Cache configuration
.cache:
  cache: &cache
    key: $CI_COMMIT_REF_SLUG
    paths:
      - ./node_modules
      - ./.pnpm-store
    policy: pull

# Rules for when to run jobs
.rules:default-branch-or-mr:
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

.rules:default-branch:
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

install:
  stage: install
  image: europe-docker.pkg.dev/metro-markets-cps-artifacts/docker-base/mpdev/monorepo-docker-base/dev-nodejs:20
  extends:
    - .rules:default-branch-or-mr
  script:
    - pnpm i --frozen-lockfile
  cache:
    <<: *cache
    policy: pull-push
  tags:
    - standard-1

lint:
  stage: lint
  image: europe-docker.pkg.dev/metro-markets-cps-artifacts/docker-base/mpdev/monorepo-docker-base/dev-nodejs:20
  extends:
    - .rules:default-branch-or-mr
  script:
    - pnpm lint
  cache:
    <<: *cache
    policy: pull
  needs:
    - job: install
  tags:
    - standard-2

test:
  stage: test
  image: europe-docker.pkg.dev/metro-markets-cps-artifacts/docker-base/mpdev/monorepo-docker-base/dev-nodejs:20
  extends:
    - .rules:default-branch-or-mr
  script:
    - pnpm test:coverage
  cache:
    <<: *cache
    policy: pull
  needs:
    - job: install
  artifacts:
    paths:
      - coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    when: always
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  tags:
    - standard-2

build:
  stage: build
  image: europe-docker.pkg.dev/metro-markets-cps-artifacts/docker-base/mpdev/monorepo-docker-base/dev-nodejs:20
  extends:
    - .rules:default-branch-or-mr
  script:
    - pnpm build
  cache:
    <<: *cache
    policy: pull
  needs:
    - job: install
    - job: lint
    - job: test
  artifacts:
    expire_in: 1 hour
    paths:
      - dist/
  tags:
    - standard-2

publish:
  stage: publish
  image: europe-docker.pkg.dev/metro-markets-cps-artifacts/docker-base/mpdev/monorepo-docker-base/dev-nodejs:20
  extends:
    - .rules:default-branch-or-mr
  script:
    - bash ./scripts/publish.sh
  cache:
    <<: *cache
    policy: pull
  needs:
    - job: build
      artifacts: true
  tags:
    - standard-2
