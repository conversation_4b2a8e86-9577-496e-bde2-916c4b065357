import type { Preview } from '@storybook/react'
import React from 'react'
import '../lib/tailwind-entry.css'
import './i18next'
import { MessageCenterDecorator } from '../stories/decorators/MessageCenterDecorator'

// Import Lato font from Google Fonts
const link = document.createElement('link')
link.href =
  'https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700;900&display=swap'
link.rel = 'stylesheet'
document.head.appendChild(link)

// Add the mc class to the document body for Tailwind scoping
if (typeof document !== 'undefined') {
  document.body.classList.add('mc')
  document.body.style.fontFamily = 'Lato, sans-serif'
}

const preview: Preview = {
  decorators: [
    MessageCenterDecorator,
    (Story) => (
      <div className="mc">
        <Story />
      </div>
    ),
  ],
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/,
      },
    },
    viewport: {
      viewports: {
        mobile: {
          name: 'Mobile',
          styles: { width: '375px', height: '667px' },
        },
        tablet: {
          name: 'Tablet',
          styles: { width: '768px', height: '1024px' },
        },
        desktop: {
          name: 'Desktop',
          styles: { width: '1024px', height: '768px' },
        },
      },
    },
    a11y: {
      element: '#storybook-root',
      config: {},
      options: {},
      manual: true,
    },
    docs: {
      autodocs: 'tag',
    },
  },
  globalTypes: {
    locale: {
      name: 'Locale',
      description: 'Internationalization locale',
      defaultValue: 'en',
      toolbar: {
        icon: 'globe',
        items: [
          { value: 'en', title: 'English' },
          { value: 'de', title: 'Deutsch' },
          { value: 'es', title: 'Español' },
          { value: 'fr', title: 'Français' },
          { value: 'it', title: 'Italiano' },
          { value: 'nl', title: 'Nederlands' },
          { value: 'pt', title: 'Português' },
        ],
      },
    },
  },
}

export default preview
