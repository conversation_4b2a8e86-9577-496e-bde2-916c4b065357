# Translations

Remember to tag SDK-related keys in Phrase with the "mc" tag. When you run phrase pull in the SDK, it will now only consider tagged keys, ensuring our JSON files remain concise and organized.

# Troubleshooting

If you get this kind of error in client app or in SDK itself:

```
@metromarkets/message-center-sdk is not in the npm registry, or you have no permission to fetch it
```

you have to login to the npm registry first:

```bash
npm login --registry=https://artifactory.cps-artifacts.metro-markets.org/artifactory/api/npm/npm-hosted-mp/
```
