// For more info, see https://github.com/storybookjs/eslint-plugin-storybook#configuration-flat-config-format
import storybook from 'eslint-plugin-storybook'

import globals from 'globals'
import pluginReact from 'eslint-plugin-react'
import testingLibrary from 'eslint-plugin-testing-library'
import jsxA11y from 'eslint-plugin-jsx-a11y'
import prettier from 'eslint-plugin-prettier'
import importQuotes from 'eslint-plugin-import-quotes'
import * as tseslint from '@typescript-eslint/eslint-plugin'
import tseslintParser from '@typescript-eslint/parser'

export default [
  {
    ignores: ['dist/'],
  },
  {
    languageOptions: {
      globals: {
        ...globals.browser,
        ...globals.node,
      },
    },
  },
  {
    linterOptions: {
      reportUnusedDisableDirectives: true,
    },
  },
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      parser: tseslintParser,
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    plugins: {
      react: pluginReact,
      'jsx-a11y': jsxA11y,
      testing: testingLibrary,
      prettier: prettier,
      import: importQuotes,
      '@typescript-eslint': tseslint,
    },
    rules: {
      quotes: [2, 'single', 'avoid-escape'],
      'react/no-unknown-property': 'off',
      semi: ['error', 'never'],
      'no-console': 2,
      'no-unused-vars': 1,
      'jsx-a11y/alt-text': 1,
      'jsx-a11y/anchor-has-content': 1,
      'jsx-a11y/aria-props': 1,
      'jsx-a11y/aria-role': [
        1,
        {
          allowedInvalidRoles: ['text'],
          ignoreNonDOM: true,
        },
      ],
      'jsx-a11y/no-noninteractive-element-interactions': [
        1,
        {
          handlers: [
            'onClick',
            'onMouseDown',
            'onMouseUp',
            'onKeyPress',
            'onKeyDown',
            'onKeyUp',
          ],
        },
      ],
      'jsx-a11y/aria-unsupported-elements': 1,
      'jsx-a11y/autocomplete-valid': 1,
      'jsx-a11y/click-events-have-key-events': 1,
      'jsx-a11y/control-has-associated-label': 1,
      'jsx-a11y/heading-has-content': 1,
      'jsx-a11y/html-has-lang': 1,
      'jsx-a11y/iframe-has-title': 1,
      'jsx-a11y/img-redundant-alt': 1,
      'jsx-a11y/interactive-supports-focus': 1,
      'jsx-a11y/label-has-associated-control': 1,
      'jsx-a11y/lang': 1,
      'jsx-a11y/media-has-caption': 1,
    },
  },
  {
    files: [
      '**/__tests__/**/*.[jt]s?(x)',
      '**/?(*.)+(spec|test).[jt]s?(x)',
      'pages/*',
      'pages/*.tsx',
      'pages/api/*',
      'pages/*/testpage.tsx',
      'core/services/i18.ts',
      'core/ssr/featureFlag/*',
    ],
    rules: {
      'import/no-default-export': 0,
    },
  },
  {
    files: ['**/*.stories.*'],
    rules: {
      'import/no-default-export': 0,
    },
  },
  ...storybook.configs['flat/recommended'],
]
