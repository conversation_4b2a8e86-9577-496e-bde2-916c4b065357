import { AppVariant, Chat } from '@common/types'
import { ChatCard } from '@common/components/ChatCard/ChatCard'
import { Container } from '@common/components/'
import { FC, useEffect, useRef, useState } from 'react'
import { SearchChat } from '@common/components/SearchChat/SearchChat'
import { NoSearchResult } from '@common/components/NoSearchResult/NoSearchResult'
import classNames from 'classnames'
import { isElementInViewport } from '@common/utils/element-position.util'
import { useTranslation } from 'react-i18next'

interface Props {
  chats: Chat[]
  onSearch: (searchText: string) => void
  onSelectChat: (chat: Chat) => void
  selectedChat?: Chat
}

export const BuyerChatList: FC<Props> = ({
  chats,
  onSelectChat,
  onSearch,
  selectedChat,
}) => {
  const [searchText, setSearchText] = useState('')
  const noSearchResult = searchText !== '' && chats.length === 0
  const chatRefs = useRef<
    Record<string, HTMLDivElement | HTMLLIElement | null>
  >({})
  const chatContainerRef = useRef<HTMLDivElement>(null)
  const { t } = useTranslation()

  useEffect(() => {
    onSearch(searchText)
  }, [searchText])

  useEffect(() => {
    if (selectedChat?.id) {
      scrollToChat(selectedChat.id)
    }
  }, [selectedChat])

  const scrollToChat = (chatId: string) => {
    const chatEl = chatRefs.current[chatId]

    if (chatEl && !isElementInViewport(chatEl, 'mc-overflow-y-scroll')) {
      const scrollContainer = chatContainerRef.current

      if (scrollContainer) {
        const containerTop = scrollContainer.getBoundingClientRect().top
        const elTop = chatEl.getBoundingClientRect().top

        const offset = elTop - containerTop
        scrollContainer.scrollTop += offset
      }
    }
  }

  if (!chats) {
    return <></>
  }

  return (
    <Container>
      <div className="mc-sticky mc-top-[0px]">
        <SearchChat onChange={setSearchText} />
        {noSearchResult && <NoSearchResult searchText={searchText} />}
      </div>
      <div
        className={classNames('mc-overflow-y-scroll', {
          'mc-h-[calc(100%-55px)]': chats.length > 0,
          'mc-h-auto': chats.length === 0,
        })}
        ref={chatContainerRef}
      >
        <ul aria-label={t('MESSAGE_CENTER.CHAT_LIST')}>
          {chats.map((chat) => (
            <li
              key={chat.id}
              ref={(el) => {
                chatRefs.current[chat.id] = el
              }}
            >
              <ChatCard
                onClick={() => onSelectChat(chat)}
                chat={chat}
                variant={AppVariant.Buyer}
              />
            </li>
          ))}
        </ul>
      </div>
    </Container>
  )
}
