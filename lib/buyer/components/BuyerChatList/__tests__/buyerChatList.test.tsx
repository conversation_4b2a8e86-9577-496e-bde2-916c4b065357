import { render, screen, within } from '@testing-library/react'
import { BuyerChatList } from '../BuyerChatList'
import { Chat } from '@common/types'
import { vi } from 'vitest'
import { mockChatList } from '@common/mocks/chatlist'

vi.mock('@common/hooks/useSubject', () => ({
  useSubject: () => ({
    getSubject: (chat) => chat.subject,
  }),
}))
vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

describe('BuyerChatList', async () => {
  const onSelectChat = vi.fn()
  const onSearch = vi.fn()

  it('renders chat cards without SalesChannelBadge for buyer variant', () => {
    render(
      <BuyerChatList
        chats={mockChatList}
        onSearch={onSearch}
        onSelectChat={onSelectChat}
      />
    )
    const salesChannelBadges = screen.queryAllByTestId('sales-channel-badge')

    expect(salesChannelBadges.length).toBe(0)
  })

  it.each(mockChatList)('renders chat card with title: %s', (chat: Chat) => {
    render(
      <BuyerChatList
        chats={mockChatList}
        onSearch={onSearch}
        onSelectChat={onSelectChat}
      />
    )
    const chatCard = screen.getByText(chat.order.orderNumber).closest('div')
    expect(within(chatCard).getByText(chat.subject)).toBeInTheDocument()
  })

  it('calls onSelectChat when chat card is clicked', () => {
    // Given
    render(
      <BuyerChatList
        chats={mockChatList}
        onSearch={onSearch}
        onSelectChat={onSelectChat}
      />
    )
    const chatCard = screen.getByText(mockChatList[0].subject)

    // When
    chatCard.click()

    // Then
    expect(onSelectChat).toHaveBeenCalledWith(mockChatList[0])
  })

  it('scrolls to the selected chat if it is not in the viewport', () => {
    const mockScrollTop = 0
    const mockOffset = 100
    const scrollContainer = document.createElement('div')

    Object.defineProperty(scrollContainer, 'getBoundingClientRect', {
      value: () => ({
        top: 0,
      }),
    })

    const chatElement = document.createElement('li')
    Object.defineProperty(chatElement, 'getBoundingClientRect', {
      value: () => ({
        top: mockOffset,
      }),
    })

    vi.mock('@common/utils/element-position.util', async () => {
      const actual = await vi.importActual<any>(
        '@common/utils/element-position.util'
      )
      return {
        ...actual,
        isElementInViewport: () => false,
      }
    })

    const { container } = render(
      <BuyerChatList
        chats={mockChatList}
        onSearch={onSearch}
        onSelectChat={onSelectChat}
        selectedChat={mockChatList[0]}
      />
    )

    const scrollContainerInDom = container.querySelector(
      '.mc-overflow-y-scroll'
    ) as HTMLDivElement

    scrollContainerInDom.scrollTop = mockScrollTop

    const offset =
      chatElement.getBoundingClientRect().top -
      scrollContainer.getBoundingClientRect().top
    scrollContainerInDom.scrollTop += offset

    expect(scrollContainerInDom.scrollTop).toBe(mockOffset)
  })
})
