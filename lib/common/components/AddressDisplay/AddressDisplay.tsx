import { Address } from '@common/types'
import { FC, Fragment } from 'react'

interface Props {
  address?: Address
}

export const AddressDisplay: FC<Props> = ({ address }) => {
  if (!address) {
    return null
  }

  const lines = [
    address.companyName,
    [address.firstName, address.lastName].filter(Boolean).join(' '),
    [address.street, address.houseNumberOrName].filter(Boolean).join(' '),
    [address.postalCode, address.city].filter(Boolean).join(' '),
  ].filter(Boolean)

  return (
    <>
      {lines.map((line, index) => (
        <Fragment key={index}>
          {line}
          {index < lines.length - 1 && <br />}
        </Fragment>
      ))}
    </>
  )
}
