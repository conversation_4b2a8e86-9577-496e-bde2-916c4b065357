import { render } from '@testing-library/react'
import { AddressDisplay } from '../AddressDisplay'
import { Address } from '@common/types'

const fullAddress: Address = {
  firstName: 'Erika',
  lastName: 'Mustermann',
  companyName: 'Beispiel GmbH',
  street: 'Hauptstraße',
  houseNumberOrName: '101',
  postalCode: '10115',
  city: 'Berlin',
}

const getFormattedLines = (address: Partial<Address>) => ({
  company: address.companyName,
  name: [address.firstName, address.lastName].filter(Boolean).join(' '),
  street: [address.street, address.houseNumberOrName].filter(Boolean).join(' '),
  cityLine: [address.postalCode, address.city].filter(Boolean).join(' '),
})

describe('AddressDisplay', () => {
  it('renders a full address correctly', () => {
    const { container } = render(<AddressDisplay address={fullAddress} />)
    const expected = getFormattedLines(fullAddress)

    expect(container).toHaveTextContent(expected.company)
    expect(container).toHaveTextContent(expected.name)
    expect(container).toHaveTextContent(expected.street)
    expect(container).toHaveTextContent(expected.cityLine)
  })

  it('renders nothing if no address is provided', () => {
    const { container } = render(<AddressDisplay />)
    expect(container).toBeEmptyDOMElement()
  })

  it('gracefully handles a sparse address with missing fields', () => {
    const sparseAddress: Address = {
      street: 'Nebenweg',
      houseNumberOrName: '5a',
      city: 'München',
    }
    const { container } = render(<AddressDisplay address={sparseAddress} />)

    expect(container).toHaveTextContent('Nebenweg 5a')
    expect(container).toHaveTextContent('München')

    expect(container.textContent).not.toContain('undefined')
    expect(container.querySelectorAll('br').length).toBe(1)
  })

  it('correctly omits a line if all its parts are missing', () => {
    const addressWithoutName: Address = {
      ...fullAddress,
      firstName: undefined,
      lastName: undefined,
    }
    const { container } = render(
      <AddressDisplay address={addressWithoutName} />
    )
    const expected = getFormattedLines(fullAddress)

    expect(container).not.toHaveTextContent(expected.name)
    expect(container).toHaveTextContent(expected.company)
  })
})
