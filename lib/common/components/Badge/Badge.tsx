import cs from 'classnames'

interface BadgeProps {
  children: React.ReactNode
  small?: boolean
  className?: string
  ariaLabel?: string
}

export const Badge = ({
  children,
  className,
  small,
  ariaLabel,
}: BadgeProps): JSX.Element => {
  return (
    <div
      className={cs(
        'mc-rounded-20 mc-text-metro-blue-shade-10',
        {
          'mc-px-10 mc-py-2': !small,
          'mc-px-8': small,
        },
        className
      )}
      aria-label={ariaLabel}
    >
      {children}
    </div>
  )
}
