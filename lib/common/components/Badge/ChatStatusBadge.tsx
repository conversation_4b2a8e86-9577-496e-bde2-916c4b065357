import { useTranslation } from 'react-i18next'
import { Badge } from './Badge'
import classNames from 'classnames'
import { CHAT_STATUS_TEXT, ChatBadgeStatus } from '@common/constants'

interface Props {
  status: ChatBadgeStatus
  small?: boolean
}

export const ChatStatusBadge = ({ status, small }: Props): JSX.Element => {
  const { t } = useTranslation()

  const className = classNames({
    'mc-bg-grey-tint-80 mc-border-[1px] mc-border-grey-tint-50':
      status === ChatBadgeStatus.NEW,
    'mc-bg-orange-tint-80 mc-border-[1px] mc-border-metro-orange':
      status === ChatBadgeStatus.NEEDS_REPLY,
    'mc-bg-red-tint-80 mc-border-[1px] mc-border-red-tint-80':
      status === ChatBadgeStatus.OVER_SLA,
    'mc-bg-grey-tint-60 mc-border-[1px] mc-border-grey-tint-80':
      status === ChatBadgeStatus.INACTIVE,
  })

  const defaultClassName =
    'mc-bg-grey-tint-95 mc-border-[1px] mc-border-grey-tint-80'

  return (
    <Badge
      className={className || defaultClassName}
      small={small}
      ariaLabel={`${t('MESSAGE_CENTER.CHAT_STATUS')} ${t(CHAT_STATUS_TEXT[status])}`}
    >
      {t(CHAT_STATUS_TEXT[status])}
    </Badge>
  )
}
