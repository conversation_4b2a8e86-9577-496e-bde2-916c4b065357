import { FC, Fragment } from 'react'
import { AppVariant, Chat } from '@common/types'
import { ChatBadgeStatus } from '@common/constants'
import { ChatStatusBadge } from '@common/components/Badge/ChatStatusBadge'

interface Props {
  chat: Chat
  variant: AppVariant
}

export const ChatStatusBadges: FC<Props> = ({ chat, variant }) => {
  const isNewChat =
    chat.id === 'new' || chat.id === null || chat.lastMessageAt === null

  const getApplicableStatuses = (): ChatBadgeStatus[] => {
    const statuses: ChatBadgeStatus[] = []

    if (
      isNewChat &&
      (variant === AppVariant.Buyer || variant === AppVariant.Seller)
    ) {
      statuses.push(ChatBadgeStatus.NEW)
    }

    if (variant === AppVariant.Seller || variant === AppVariant.Employee) {
      if (chat.overSLA) {
        statuses.push(ChatBadgeStatus.OVER_SLA)
      } else if (chat.needsReply) {
        statuses.push(ChatBadgeStatus.NEEDS_REPLY)
      }
    }

    return statuses
  }

  const statusesToRender = getApplicableStatuses()

  if (statusesToRender.length === 0) {
    return null
  }

  return (
    <Fragment>
      {statusesToRender.map((status) => (
        <ChatStatusBadge key={status} status={status} />
      ))}
    </Fragment>
  )
}
