import { useTranslation } from 'react-i18next'
import { Badge } from './Badge'
import classNames from 'classnames'
import {
  ORDER_LINE_STATUS_TEXT_SELLER,
  ORDER_LINE_STATUS_TEXT_BUYER,
} from '@common/constants'
import { AppVariant, OrderLineStatus } from '@common/types'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'

interface Props {
  status: OrderLineStatus
  small?: boolean
}

export const OrderLineStatusBadge = ({ status }: Props): JSX.Element => {
  const { t } = useTranslation()
  const { settings } = useMessageCenterContext()
  const statusPhrase =
    settings.appVariant === AppVariant.Seller
      ? ORDER_LINE_STATUS_TEXT_SELLER[status]
      : ORDER_LINE_STATUS_TEXT_BUYER[status]

  const className = classNames({
    'mc-bg-[#FFECCC]': status === OrderLineStatus.RETURN_DECLINED,
    'mc-bg-[#CCE0FF]': [
      OrderLineStatus.SHIPPED,
      OrderLineStatus.RETURN_REQUEST,
      OrderLineStatus.RETURN_REQUESTED,
    ].includes(status),
    'mc-bg-[#F2AEA4]': status === OrderLineStatus.CANCELED,
    'mc-bg-[#FFFBCC]': status === OrderLineStatus.CONFIRMED,
    'mc-bg-[#F8D7D1]': status === OrderLineStatus.WAITING_FOR_PAYMENT,
    'mc-bg-[#D6F2E6]': status === OrderLineStatus.RETURN_ACCEPTED,
    'mc-bg-[#002866] mc-text-white-main':
      status === OrderLineStatus.PENDING_VERIFICATION,
    'mc-bg-[#6681AA] mc-text-white-main': status === OrderLineStatus.BLOCKED,
  })

  const defaultClassName =
    'mc-bg-grey-tint-95 mc-border-[1px] mc-border-grey-tint-80'

  return (
    <div className="mc-flex mc-justify-between">
      <Badge className={className || defaultClassName} small>
        {t(statusPhrase)}
      </Badge>
    </div>
  )
}
