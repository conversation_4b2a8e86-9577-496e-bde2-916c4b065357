import { useTranslation } from 'react-i18next'
import { Badge } from './Badge'
import classNames from 'classnames'
import { OrderStatus } from '@common/types'

interface Props {
  status: OrderStatus
  isOverSLA?: boolean
  small?: boolean
}

export const OrderStatusBadge = ({
  status,
  isOverSLA,
  small,
}: Props): JSX.Element => {
  const { t } = useTranslation()

  const className = classNames({
    'mc-bg-[#F2AEA4]': status === OrderStatus.CANCELED,
    'mc-bg-[#FFFBCC]': status === OrderStatus.CONFIRMED,
    'mc-bg-[#F8D7D1]': status === OrderStatus.PAYMENT_FAILED,
    'mc-bg-[#D6F2E6]': status === OrderStatus.PAID,
    'mc-bg-[#002866] mc-text-white-main':
      status === OrderStatus.WAITING_FOR_APPROVAL,
    'mc-bg-[#6681AA] mc-text-white-main': status === OrderStatus.MIXED,
  })

  const defaultClassName =
    'mc-bg-grey-tint-95 mc-border-[1px] mc-border-grey-tint-80'

  return (
    <div className="mc-flex mc-justify-between">
      <Badge className={className || defaultClassName} small={small}>
        {status}
      </Badge>
      {isOverSLA && (
        <Badge className={className || defaultClassName} small={small}>
          {t('MESSAGE_CENTER.CHAT_STATUS.OVER_SLA')}
        </Badge>
      )}
    </div>
  )
}
