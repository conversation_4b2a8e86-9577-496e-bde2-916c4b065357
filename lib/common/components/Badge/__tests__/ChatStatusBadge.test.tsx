import React from 'react'
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { ChatStatusBadge } from '../ChatStatusBadge'
import { ChatBadgeStatus, CHAT_STATUS_TEXT } from '@common/constants'

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

describe('ChatStatusBadge', () => {
  it('renders with default props', () => {
    const { container } = render(
      <ChatStatusBadge status={ChatBadgeStatus.NEW} />
    )

    expect(container).toBeInTheDocument()
    expect(
      screen.getByText(CHAT_STATUS_TEXT[ChatBadgeStatus.NEW])
    ).toBeInTheDocument()
  })

  it('renders OVER_SLA status correctly', () => {
    render(<ChatStatusBadge status={ChatBadgeStatus.OVER_SLA} />)

    expect(
      screen.getByText(CHAT_STATUS_TEXT[ChatBadgeStatus.OVER_SLA])
    ).toBeInTheDocument()
  })

  it('renders with small prop', () => {
    const { container } = render(
      <ChatStatusBadge status={ChatBadgeStatus.NEW} small />
    )

    expect(container.querySelector('.mc-px-8')).toBeInTheDocument()
  })
})
