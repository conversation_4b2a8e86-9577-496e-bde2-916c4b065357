import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { ChatStatusBadges } from '../ChatStatusBadges'
import { AppVariant, Chat } from '@common/types'

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

const mockChat: Chat = {
  id: 'chat-001',
  subject: 'Subject',
  customSubject: null,
  createdAt: '2025-06-15T12:00:00Z',
  isSelected: false,
  initiatorUserType: 'BUYER',
  isSeen: true,
  buyer: { id: 'b1', firstName: 'Alice', lastName: 'Smith' },
  seller: {
    accounts: [],
    organization: { id: 'org1', shopName: 'WidgetWorks' },
  },
  order: { id: 'o123', orderNumber: 'ORD-12345', salesChannel: 'DE' },
  lastMessageAt: '2025-06-17T14:45:00Z',
  lastMessageByUserType: 'BUYER',
  needsReply: false,
  overSLA: false,
  inactive: false,
}

describe('ChatStatusBadges', () => {
  describe('Buyer Variant', () => {
    it('renders NEW badge for a new chat', () => {
      const newChat = { ...mockChat, id: 'new' }
      render(<ChatStatusBadges chat={newChat} variant={AppVariant.Buyer} />)
      expect(
        screen.getByText('MESSAGE_CENTER.CHAT_STATUS.NEW')
      ).toBeInTheDocument()
    })

    it('does not render seller-specific badges', () => {
      const sellerStatusChat = { ...mockChat, needsReply: true, overSLA: true }
      render(
        <ChatStatusBadges chat={sellerStatusChat} variant={AppVariant.Buyer} />
      )
      expect(
        screen.queryByText('MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY')
      ).not.toBeInTheDocument()
      expect(
        screen.queryByText('MESSAGE_CENTER.CHAT_STATUS.OVER_SLA')
      ).not.toBeInTheDocument()
    })
  })

  describe('Seller Variant', () => {
    it('renders NEW badge for a new chat', () => {
      const newChat = { ...mockChat, lastMessageAt: null }
      render(<ChatStatusBadges chat={newChat} variant={AppVariant.Seller} />)
      expect(
        screen.getByText('MESSAGE_CENTER.CHAT_STATUS.NEW')
      ).toBeInTheDocument()
    })

    it('renders NEEDS_REPLY badge when appropriate', () => {
      const needsReplyChat = { ...mockChat, needsReply: true }
      render(
        <ChatStatusBadges chat={needsReplyChat} variant={AppVariant.Seller} />
      )
      expect(
        screen.getByText('MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY')
      ).toBeInTheDocument()
    })

    it('renders OVER_SLA badge when appropriate', () => {
      const overSlaChat = { ...mockChat, overSLA: true }
      render(
        <ChatStatusBadges chat={overSlaChat} variant={AppVariant.Seller} />
      )
      expect(
        screen.getByText('MESSAGE_CENTER.CHAT_STATUS.OVER_SLA')
      ).toBeInTheDocument()
    })

    it('renders only OVER_SLA when both overSLA and needsReply are true', () => {
      const bothStatusChat = {
        ...mockChat,
        needsReply: true,
        overSLA: true,
      }
      render(
        <ChatStatusBadges chat={bothStatusChat} variant={AppVariant.Seller} />
      )
      expect(
        screen.getByText('MESSAGE_CENTER.CHAT_STATUS.OVER_SLA')
      ).toBeInTheDocument()
      expect(
        screen.queryByText('MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY')
      ).not.toBeInTheDocument()
    })
  })

  describe('Employee Variant', () => {
    it('does not render NEW badge', () => {
      const newChat = { ...mockChat, id: 'new' }
      render(<ChatStatusBadges chat={newChat} variant={AppVariant.Employee} />)
      expect(
        screen.queryByText('MESSAGE_CENTER.CHAT_STATUS.NEW')
      ).not.toBeInTheDocument()
    })

    it('renders NEEDS_REPLY badge when appropriate', () => {
      const needsReplyChat = { ...mockChat, needsReply: true }
      render(
        <ChatStatusBadges chat={needsReplyChat} variant={AppVariant.Employee} />
      )
      expect(
        screen.getByText('MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY')
      ).toBeInTheDocument()
    })

    it('renders OVER_SLA badge when appropriate', () => {
      const overSlaChat = { ...mockChat, overSLA: true }
      render(
        <ChatStatusBadges chat={overSlaChat} variant={AppVariant.Employee} />
      )
      expect(
        screen.getByText('MESSAGE_CENTER.CHAT_STATUS.OVER_SLA')
      ).toBeInTheDocument()
    })
  })
})
