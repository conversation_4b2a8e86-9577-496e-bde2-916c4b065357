import { describe, it, expect, vi } from 'vitest'
import { render, screen, within } from '@testing-library/react'
import { OrderLineStatusBadge } from '../OrderLineStatusBadge'
import { OrderLineStatus, AppVariant } from '@common/types'
import {
  ORDER_LINE_STATUS_TEXT_SELLER,
  ORDER_LINE_STATUS_TEXT_BUYER,
} from '@common/constants'
import {
  MessageCenterContext,
  defaultSettings,
} from '@common/contexts/MessageCenterContext'

// Mock the translation function
vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

describe('OrderLineStatusBadge Component', () => {
  // Helper function to render with context
  const renderWithContext = (
    ui: React.ReactNode,
    appVariant = AppVariant.Seller
  ) => {
    const settings = {
      ...defaultSettings,
      appVariant,
    }
    return render(
      <MessageCenterContext.Provider value={{ settings, subjects: [] }}>
        {ui}
      </MessageCenterContext.Provider>
    )
  }

  it('renders status text correctly', () => {
    renderWithContext(<OrderLineStatusBadge status={OrderLineStatus.SHIPPED} />)
    expect(
      screen.getByText(ORDER_LINE_STATUS_TEXT_SELLER[OrderLineStatus.SHIPPED])
    ).toBeInTheDocument()
  })

  it('applies correct class for RETURN_DECLINED status', () => {
    const { container } = renderWithContext(
      <OrderLineStatusBadge status={OrderLineStatus.RETURN_DECLINED} />
    )
    const badge = within(container)
      .getByText(ORDER_LINE_STATUS_TEXT_SELLER[OrderLineStatus.RETURN_DECLINED])
      .closest('div')

    expect(badge).toHaveClass('mc-bg-[#FFECCC]')
  })

  it('applies correct class for SHIPPED status', () => {
    const { container } = renderWithContext(
      <OrderLineStatusBadge status={OrderLineStatus.SHIPPED} />
    )
    const badge = within(container)
      .getByText(ORDER_LINE_STATUS_TEXT_SELLER[OrderLineStatus.SHIPPED])
      .closest('div')

    expect(badge).toHaveClass('mc-bg-[#CCE0FF]')
  })

  it('applies correct class for CANCELED status', () => {
    const { container } = renderWithContext(
      <OrderLineStatusBadge status={OrderLineStatus.CANCELED} />
    )
    const badge = within(container)
      .getByText(ORDER_LINE_STATUS_TEXT_SELLER[OrderLineStatus.CANCELED])
      .closest('div')

    expect(badge).toHaveClass('mc-bg-[#F2AEA4]')
  })

  it('applies correct class for RETURN_REQUEST status', () => {
    const { container } = renderWithContext(
      <OrderLineStatusBadge status={OrderLineStatus.RETURN_REQUEST} />
    )
    const badge = within(container)
      .getByText(ORDER_LINE_STATUS_TEXT_SELLER[OrderLineStatus.RETURN_REQUEST])
      .closest('div')

    expect(badge).toHaveClass('mc-bg-[#CCE0FF]')
  })

  it('applies default class when status has no specific styling', () => {
    // For this test, we'll use PLACED status which doesn't have specific styling in the component
    const { container } = renderWithContext(
      <OrderLineStatusBadge status={OrderLineStatus.PLACED} />
    )
    const badge = within(container)
      .getByText(ORDER_LINE_STATUS_TEXT_SELLER[OrderLineStatus.PLACED])
      .closest('div')

    expect(badge).toHaveClass('mc-bg-grey-tint-95')
    expect(badge).toHaveClass('mc-border-[1px]')
    expect(badge).toHaveClass('mc-border-grey-tint-80')
  })

  it('uses buyer text when app variant is Buyer', () => {
    renderWithContext(
      <OrderLineStatusBadge status={OrderLineStatus.RETURN_REQUESTED} />,
      AppVariant.Buyer
    )
    expect(
      screen.getByText(
        ORDER_LINE_STATUS_TEXT_BUYER[OrderLineStatus.RETURN_REQUESTED]
      )
    ).toBeInTheDocument()
  })
})
