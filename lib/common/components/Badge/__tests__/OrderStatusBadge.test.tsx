import { describe, it, expect } from 'vitest'
import { render, screen, within } from '@testing-library/react'
import { OrderStatusBadge } from '../OrderStatusBadge'
import { OrderStatus } from '@common/types'

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

describe('OrderStatusBadge Component', () => {
  it('renders status text correctly', () => {
    render(<OrderStatusBadge status={OrderStatus.PAID} />)
    expect(screen.getByText(OrderStatus.PAID)).toBeInTheDocument()
  })

  it('applies correct class for given status', () => {
    const { container } = render(
      <OrderStatusBadge status={OrderStatus.CANCELED} />
    )
    const badge = within(container)
      .getByText(OrderStatus.CANCELED)
      .closest('div')

    expect(badge).toHaveClass('mc-bg-[#F2AEA4]')
  })

  it('renders Over SLA badge when isOverSLA is true', () => {
    render(<OrderStatusBadge status={OrderStatus.CONFIRMED} isOverSLA />)
    expect(
      screen.getByText('MESSAGE_CENTER.CHAT_STATUS.OVER_SLA')
    ).toBeInTheDocument()
  })

  it('applies small class when small prop is true', () => {
    const { container } = render(
      <OrderStatusBadge status={OrderStatus.PAID} small />
    )
    const badge = within(container).getByText(OrderStatus.PAID).closest('div')

    expect(badge).toHaveClass('mc-px-8')
  })

  it('applies default class when status has no specific styling', () => {
    // Create a status that doesn't have specific styling in the component
    const mockStatus = 'UNKNOWN_STATUS' as OrderStatus
    const { container } = render(<OrderStatusBadge status={mockStatus} />)
    const badge = within(container).getByText(mockStatus).closest('div')

    expect(badge).toHaveClass('mc-bg-grey-tint-95')
    expect(badge).toHaveClass('mc-border-[1px]')
    expect(badge).toHaveClass('mc-border-grey-tint-80')
  })
})
