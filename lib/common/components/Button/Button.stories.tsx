import type { Meta, StoryObj } from '@storybook/react-vite'
import { action } from 'storybook/actions'
import Button from './Button'
import { SVG_NAMES } from '@common/icons'

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },

  argTypes: {
    disabled: {
      control: 'boolean',
      description: 'Disables the button',
    },
    secondary: {
      control: 'boolean',
      description: 'Shows secondary button style',
    },
    children: {
      control: 'text',
      description: 'Button text content',
    },
  },
}

export default meta
type Story = StoryObj<typeof Button>

export const Primary: Story = {
  args: {
    children: 'Send Message',
    icon: SVG_NAMES.SEND,
    onClick: action('Button clicked'),
    disabled: false,
    secondary: false,
  },
}

export const Secondary: Story = {
  args: {
    children: 'Upload File',
    icon: SVG_NAMES.UPLOAD,
    onClick: action('Upload clicked'),
    disabled: false,
    secondary: true,
  },
}

export const Disabled: Story = {
  args: {
    children: 'Disabled But<PERSON>',
    icon: SVG_NAMES.SEND_DISABLED,
    onClick: action('This should not fire'),
    disabled: true,
    secondary: false,
  },
}

export const WithSearch: Story = {
  args: {
    children: 'Search',
    icon: SVG_NAMES.SEARCH,
    onClick: action('Search clicked'),
    disabled: false,
    secondary: false,
  },
}
