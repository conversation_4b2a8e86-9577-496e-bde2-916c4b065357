import classNames from 'classnames'
import { FC } from 'react'
import { SVGIcon } from '@common/components/SVGIcon/SVGIcon'
import { px2rem } from '@common/utils/px2rem'

type Props = React.ButtonHTMLAttributes<HTMLButtonElement> & {
  className?: string
  invalid?: boolean
  disabled?: boolean
  secondary?: boolean
  icon: string
  children: React.ReactNode
  onClick: () => void
  iconClassName?: string
}

const Button: FC<Props> = ({
  disabled,
  secondary,
  children,
  className,
  icon,
  onClick,
  iconClassName,
  ...rest
}) => {
  return (
    <button
      onClick={() => onClick()}
      {...rest}
      disabled={disabled}
      className={classNames(
        className,
        'mc-rounded-6 mc-bg-blue-base mc-relative mc-pr-16 mc-pl-36 mc-h-40 mc-flex mc-font-bold',
        'mc-items-center mc-justify-between ',
        {
          'mc-text-white-main                                                                                           ':
            !secondary,
        },
        { 'mc-bg-blue-secondary mc-text-blue-base': secondary },
        {
          '!mc-bg-grey-tint-95 !mc-text-grey-disabled !mc-bg-btn-disabled-diagonal-lines':
            disabled,
        }
      )}
    >
      {icon && (
        <SVGIcon
          dataTestid="search-icon"
          name={icon}
          width={px2rem(20)}
          height={px2rem(20)}
          className={classNames(
            'mc-block mc-align-middle mc-absolute mc-top-10 mc-left-10',
            iconClassName
          )}
        />
      )}
      {children}
    </button>
  )
}

export default Button
