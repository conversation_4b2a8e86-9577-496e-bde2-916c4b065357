import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { SVG_NAMES } from '@common/icons'
describe('Button Component', () => {
  it('should render correctly with props', () => {
    const handleClick = vi.fn()
    render(
      <Button icon={SVG_NAMES.SEARCH} onClick={handleClick}>
        Send
      </Button>
    )
    expect(screen.getByTestId('mc-send-button')).toBeInTheDocument()
    expect(screen.getByTestId('search-icon')).toBeInTheDocument()
  })

  it('should call onClick when clicked', () => {
    const handleClick = vi.fn()
    render(
      <Button icon={SVG_NAMES.SEARCH} onClick={handleClick}>
        Click Me
      </Button>
    )
    fireEvent.click(screen.getByTestId('mc-send-button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should be disabled when the disabled prop is true', () => {
    render(
      <Button icon={SVG_NAMES.SEARCH} onClick={() => {}} disabled>
        Click Me
      </Button>
    )
    expect(screen.getByTestId('mc-send-button')).toBeDisabled()
  })

  it('renders correctly and matches snapshot', () => {
    const mockOnSend = vi.fn()
    const { container } = render(
      <Button icon={SVG_NAMES.SEARCH} onClick={mockOnSend}>
        Send
      </Button>
    )
    expect(container).toMatchSnapshot()
  })
})
