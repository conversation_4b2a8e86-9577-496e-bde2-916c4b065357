import { FC, ReactNode } from 'react'
import { Container } from '@common/components'

export interface CardProps {
  title: string
  children: ReactNode
}

export const Card: FC<CardProps> = ({ title, children }) => {
  return (
    <Container>
      <section className="mc-py-20 mc-px-20 mc-text-metro-blue-main">
        <h2 className="mc-font-lato mc-text-xl mc-font-bold mc-pb-20 mc-text-metro-blue-main">
          {title}
        </h2>
        {children}
      </section>
    </Container>
  )
}
