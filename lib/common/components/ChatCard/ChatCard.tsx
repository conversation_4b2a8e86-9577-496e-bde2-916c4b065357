import { formatForScreenReader, parseDate } from '@common/utils/date.util'
import classNames from 'classnames'
import { SalesChannelBadge } from '@common/components/SalesChannelBadge'
import { Chat } from '@common/types'
import { AppVariant } from '@common/types/enums'
import { useSubject } from '@common/hooks/useSubject'
import { ChatStatusBadges } from '@common/components/Badge/ChatStatusBadges'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import { useTranslation } from 'react-i18next'

interface Props {
  chat: Chat
  onClick: () => void
  variant: AppVariant
}

export const ChatCard = ({ chat, onClick, variant }: Props): JSX.Element => {
  const { getSubject } = useSubject()
  const { settings } = useMessageCenterContext()
  const { t } = useTranslation()

  const isNewChat =
    chat.id === 'new' || chat.id === null || chat.lastMessageAt === null

  const isBold =
    isNewChat ||
    ((variant === AppVariant.Seller || variant === AppVariant.Buyer) &&
      !chat.isSeen)

  const fontClass = isBold ? 'mc-font-bold' : 'mc-font-normal'

  return (
    <button
      type="button"
      className={classNames(
        'mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16',
        'mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full',
        { 'mc-bg-blue-tint-95': chat.isSelected }
      )}
      onClick={(event) => {
        event.stopPropagation()
        event.preventDefault()
        onClick()
      }}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick()
        }
      }}
      data-testid="chat-card"
      aria-pressed={chat.isSelected}
      aria-label={`${t('MESSAGE_CENTER.CHAT_WITH_SELLER')
        .replace(
          '{seller}',
          variant === AppVariant.Buyer
            ? chat.seller.organization.shopName
            : `${chat.buyer.firstName} ${chat.buyer.lastName}`
        )
        .replace('{orderNumber}', chat.order.orderNumber)}`}
      aria-describedby={`chat-details-${chat.id}`}
    >
      <span id={`chat-details-${chat.id}`} className="mc-sr-only">
        {chat.id !== 'new' && chat.isSeen
          ? t('MESSAGE_CENTER.CHAT_READ')
          : t('MESSAGE_CENTER.CHAT_UNREAD')}
        .
        {chat.lastMessageAt &&
          `${t('MESSAGE_CENTER.LAST_MESSAGE_SENT_ON')} ${formatForScreenReader(chat.lastMessageAt, settings.language.toLowerCase(), `dddd, MMMM D, YYYY [${t('MESSAGE_CENTER.AT')}] h:mm A`)}`}
        .{`${t('MESSAGE_CENTER.SUBJECT')}: ${getSubject(chat)}`}
      </span>
      <div className="mc-flex mc-justify-between mc-items-center">
        <div className="mc-flex mc-items-center mc-gap-2">
          <ChatStatusBadges chat={chat} variant={variant} />
        </div>
        {chat.lastMessageAt && (
          <time
            dateTime={new Date(chat.lastMessageAt).toISOString()}
            className="leading-5 mc-font-normal mc-text-base"
          >
            {parseDate(chat.lastMessageAt)}
          </time>
        )}
      </div>
      <div
        className={classNames(
          'mc-leading-6 mc-text-regular margin mc-mt-15',
          fontClass
        )}
      >
        <p className="mc-mt-4 mc-break-words">
          {variant === AppVariant.Buyer
            ? chat.seller.organization.shopName
            : `${chat.buyer.firstName} ${chat.buyer.lastName}`}
        </p>
        <p className="mc-mt-4">{chat.order.orderNumber}</p>
        <p className="mc-mt-4 mc-break-words">{getSubject(chat)}</p>
      </div>

      {variant === AppVariant.Seller && (
        <div className="mc-mt-16">
          <SalesChannelBadge salesChannel={chat.order.salesChannel} />
        </div>
      )}
    </button>
  )
}
