import { fireEvent, render, screen } from '@testing-library/react'
import { ChatCard } from '../ChatCard'
import { Chat } from '@common/types'
import { AppVariant } from '@common/types/enums'
import { describe, it, expect, vi, beforeEach } from 'vitest'

const mockChat = {
  id: '9ef94b17-2ead-4824-8525-2e37f4080ca5',
  subject: 'BUYER.ORDERS.ORDER_TRACKING',
  customSubject: null,
  initiatorUserType: 'BUYER',
  lastMessageAt: '2025-05-22T17:37:18+00:00',
  lastMessageByUserType: 'BUYER',
  needsReply: false,
  overSLA: false,
  inactive: false,
  isSeen: true,
  createdAt: '2025-05-22T17:37:18+00:00',
  buyer: {
    id: 'a9801af2-54a7-4efa-a6a6-3cf1664dc096',
    lastName: 'Doe',
    firstName: 'John',
  },
  seller: {
    organization: {
      id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
      shopName: 'My DESHOP',
    },
    accounts: [],
  },
  order: {
    id: 'cd67be7b-6d3d-45de-8aa8-15c5612691a3',
    orderNumber: 'O25-************',
    salesChannel: 'de',
  },
} as Chat

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

vi.mock('../../../hooks/useSubject', () => ({
  useSubject: () => ({ getSubject: (chat: Chat) => chat.subject }),
}))

describe('ChatCard', () => {
  const onClickMock = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders with default props', () => {
    const { container } = render(
      <ChatCard
        chat={mockChat}
        onClick={onClickMock}
        variant={AppVariant.Seller}
      />
    )

    expect(container).toBeInTheDocument()
    expect(screen.getByText(mockChat.subject!)).toBeInTheDocument()
    expect(screen.getByText(mockChat.order.orderNumber)).toBeInTheDocument()
  })

  it('renders buyer variant correctly', async () => {
    render(
      <ChatCard
        chat={mockChat}
        onClick={onClickMock}
        variant={AppVariant.Buyer}
      />
    )

    expect(
      screen.getByText(mockChat.seller.organization.shopName)
    ).toBeInTheDocument()
    expect(
      await screen.queryByText(mockChat.order.salesChannel)
    ).not.toBeInTheDocument()
  })

  it('renders seller variant correctly', () => {
    render(
      <ChatCard
        chat={mockChat}
        onClick={onClickMock}
        variant={AppVariant.Seller}
      />
    )

    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText('DE')).toBeInTheDocument()
  })

  it('calls onClick when clicked', () => {
    render(
      <ChatCard
        chat={mockChat}
        onClick={onClickMock}
        variant={AppVariant.Seller}
      />
    )
    fireEvent.click(screen.getByText('John Doe'))

    expect(onClickMock).toHaveBeenCalledTimes(1)
  })

  describe('Font Weight Logic', () => {
    it('is bold for Buyer variant when chat is unseen', () => {
      const unseenChat = { ...mockChat, isSeen: false }
      render(
        <ChatCard
          chat={unseenChat}
          onClick={onClickMock}
          variant={AppVariant.Buyer}
        />
      )
      const textElement = screen.getByText(
        mockChat.seller.organization.shopName
      )
      expect(textElement.parentElement).toHaveClass('mc-font-bold')
    })

    it('is normal for Seller variant when chat is seen', () => {
      const seenChat = { ...mockChat, isSeen: true }
      render(
        <ChatCard
          chat={seenChat}
          onClick={onClickMock}
          variant={AppVariant.Seller}
        />
      )
      const textElement = screen.getByText('John Doe')
      expect(textElement.parentElement).toHaveClass('mc-font-normal')
    })

    it('is normal for Employee variant even when chat is unseen', () => {
      const unseenChat = { ...mockChat, isSeen: false }
      render(
        <ChatCard
          chat={unseenChat}
          onClick={onClickMock}
          variant={AppVariant.Employee}
        />
      )
      const textElement = screen.getByText('John Doe')
      expect(textElement.parentElement).toHaveClass('mc-font-normal')
    })

    it('is bold when it is a new chat, regardless of isSeen status', () => {
      const newChat = { ...mockChat, id: 'new', isSeen: true }
      render(
        <ChatCard
          chat={newChat}
          onClick={onClickMock}
          variant={AppVariant.Seller}
        />
      )
      const textElement = screen.getByText('John Doe')
      expect(textElement.parentElement).toHaveClass('mc-font-bold')
    })
  })

  describe('Accessibility', () => {
    it('describes the chat as unread for screen readers when isSeen is false', () => {
      const unseenChat = { ...mockChat, isSeen: false }
      render(
        <ChatCard
          chat={unseenChat}
          onClick={onClickMock}
          variant={AppVariant.Buyer}
        />
      )

      const button = screen.getByTestId('chat-card')
      const descriptionId = button.getAttribute('aria-describedby')
      const descriptionElement = document.getElementById(descriptionId!)

      expect(descriptionElement).toHaveTextContent('MESSAGE_CENTER.CHAT_UNREAD')
    })

    it('describes the chat as read for screen readers when isSeen is true', () => {
      const seenChat = { ...mockChat, isSeen: true }
      render(
        <ChatCard
          chat={seenChat}
          onClick={onClickMock}
          variant={AppVariant.Buyer}
        />
      )

      const button = screen.getByTestId('chat-card')
      const descriptionId = button.getAttribute('aria-describedby')
      const descriptionElement = document.getElementById(descriptionId!)

      expect(descriptionElement).toHaveTextContent('MESSAGE_CENTER.CHAT_READ')
    })
  })
})
