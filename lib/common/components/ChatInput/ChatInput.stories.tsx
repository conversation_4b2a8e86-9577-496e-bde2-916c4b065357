import type { Meta, StoryObj } from '@storybook/react-vite'
import { action } from 'storybook/actions'
import { ChatInput } from './ChatInput'

const meta: Meta<typeof ChatInput> = {
  title: 'Components/ChatInput',
  component: ChatInput,
  parameters: {
    layout: 'padded',
  },
  decorators: [
    (Story) => (
      <div style={{ maxWidth: '600px', margin: '0 auto' }}>
        <Story />
      </div>
    ),
  ],
  argTypes: {
    disabled: {
      control: 'boolean',
      description: 'Disables the entire chat input',
    },
    showAttachmentButton: {
      control: 'boolean',
      description: 'Shows or hides the attachment button',
    },
    onSubmit: {
      description: 'Called when message is submitted',
    },
    onError: {
      description: 'Called when an error occurs',
    },
  },
}

export default meta
type Story = StoryObj<typeof ChatInput>

// Mock function for getSignedUrl with action logging
const mockGetSignedUrl = async (
  files: { id: string; contentType: string }[]
) => {
  action('getSignedUrl called')(files)

  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 1000))

  const data = files.map((file) => ({
    id: file.id,
    fileId: `file-${file.id}`,
    signedUrl: `https://example.com/upload/${file.id}`,
  }))

  const response = {
    data,
    status: 'success',
  }

  action('getSignedUrl response')(response)
  return response
}

export const Default: Story = {
  args: {
    onSubmit: action('Message submitted'),
    onError: action('Error occurred'),
    getSignedUrl: mockGetSignedUrl,
    disabled: false,
    showAttachmentButton: true,
  },
}

export const WithoutAttachmentButton: Story = {
  args: {
    ...Default.args,
    showAttachmentButton: false,
  },
}

export const WithDetailedLogging: Story = {
  args: {
    onSubmit: (message) => {
      action('🚀 Send button clicked')()
      action('📝 Message content')(message.content)
      action('📎 Attachments count')(message.attachments?.length || 0)
      action('📋 Full message object')(message)
      return Promise.resolve()
    },
    onError: (error) => {
      action('❌ Error occurred')(error)
    },
    getSignedUrl: mockGetSignedUrl,
    disabled: false,
    showAttachmentButton: true,
  },
}
