import { forwardRef, useImper<PERSON><PERSON><PERSON><PERSON>, useState } from 'react'
import TextArea from '@common/components/TextArea/TextArea'
import Button from '@common/components/Button/Button'
import { SVG_NAMES } from '@common/icons'
import { useTranslation } from 'react-i18next'
import { SVGIcon } from '@common/components/SVGIcon/SVGIcon'
import { px2rem } from '@common/utils/px2rem'
import { Tooltip } from '@common/components/Tooltip/Tooltip'
import { UploadButton, useFilesUpload } from '@common/components/UploadButton'
import { TooltipContent } from './TooltipContent'
import { SignedUrlResponse } from '../UploadButton/types'
import { MessageToSave } from '@common/types'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import { isFileSizeExceeded } from '@common/utils/fileSize'
import classNames from 'classnames'
import { DISABLED_BG_DIAGONAL_LINES_CLASS } from '@common/constants'

interface Props {
  onSubmit: ({ content, attachments }: MessageToSave) => void
  onFileSelect?: (files?: File | File[]) => void
  onError: (error: any) => void
  getSignedUrl: (
    files: { id: string; contentType: string }[]
  ) => Promise<SignedUrlResponse>
  disabled?: boolean
  showAttachmentButton?: boolean
}

export interface ChatInputRef {
  clearInput: () => void
}

export const ChatInput = forwardRef<ChatInputRef, Props>(
  (
    {
      onSubmit,
      onFileSelect,
      onError,
      getSignedUrl,
      disabled,
      showAttachmentButton = true,
    },
    ref
  ) => {
    const [formDisabled, setFormDisabled] = useState(false)
    const { t } = useTranslation()
    const [text, setText] = useState('')
    const { files, addFile, uploadFiles, removeFile, setFiles } =
      useFilesUpload(getSignedUrl)
    const { settings } = useMessageCenterContext()
    const [errorMessages, setErrorMessages] = useState<string[]>([])

    useImperativeHandle(ref, () => ({
      clearInput: () => {
        setText('')
        setFiles([])
      },
    }))

    const onClick = async () => {
      if (text.trim() !== '') {
        try {
          setFormDisabled(true)
          const attachments = files.length > 0 ? await uploadFiles() : undefined
          await onSubmit({
            content: text.trim(),
            attachments,
          })
        } catch (error) {
          onError(error)
        } finally {
          setFormDisabled(false)
        }
      }
    }

    return (
      <form
        className="mc-rounded-16px mc-border-[1px] mc-border-grey-tint-80 mc-p-16 mc-flex mc-flex-col mc-bg-white-main"
        onSubmit={(e) => {
          e.preventDefault()
        }}
        aria-labelledby="chat-input-label"
      >
        <fieldset disabled={formDisabled} data-testid="message-composer">
          <label id="chat-input-label" className="mc-sr-only">
            {t('MESSAGE_CENTER.CHAT_INPUT.LABEL')}
          </label>
          <TextArea
            aria-label={t('MESSAGE_CENTER.CHAT_INPUT.LABEL')}
            autoGrow
            onChange={(event: React.ChangeEvent<HTMLTextAreaElement>) => {
              setText(event.target.value)
            }}
            value={text}
            disabled={formDisabled}
          />

          {files.length > 0 && (
            <div
              className={classNames(
                'mc-p-12 mc-rounded-[16px] mc-mt-16 mc-flex-col mc-w-max',
                {
                  [DISABLED_BG_DIAGONAL_LINES_CLASS]: formDisabled,
                  'mc-shadow-attachmentContainer': !formDisabled,
                }
              )}
              role="region"
              aria-live="polite"
              aria-label={t('MESSAGE_CENTER.CHAT_INPUT.ATTACHMENTS')}
            >
              {files.map((file) => (
                <div
                  key={file.file.name}
                  className="mc-flex mc-items-center mc-mb-1"
                >
                  {file.isLoading ? (
                    <SVGIcon
                      name={SVG_NAMES.LOADER}
                      stroke={formDisabled ? '#667284' : '#0059e4'}
                      width={px2rem(16)}
                      height={px2rem(16)}
                      className="mc-animate-spin"
                      data-testid="loading-file-icon"
                      role="status"
                      aria-hidden="true"
                    />
                  ) : (
                    <button
                      onClick={() => removeFile(file.id)}
                      aria-label={t(
                        'MESSAGE_CENTER.CHAT_INPUT.REMOVE_FILE'
                      ).replace('fileName', file.file.name)}
                    >
                      <SVGIcon
                        name={
                          formDisabled
                            ? SVG_NAMES.TRASH_DISABLED
                            : SVG_NAMES.TRASH
                        }
                        fill="#0059e4"
                        width={px2rem(16)}
                        height={px2rem(16)}
                        data-testid="delete-file-icon"
                      />
                    </button>
                  )}
                  <span
                    className={classNames(
                      'mc-font-lato mc-text-[14px] mc-font-normal mc-leading-[20px] mc-text-blue-main mc-underline mc-pl-10 mc-truncate mc-max-w-[250px] mc-block',
                      {
                        'mc-text-grey-disabled': formDisabled,
                      }
                    )}
                    title={file.file.name}
                  >
                    {file.file.name}
                  </span>
                </div>
              ))}
            </div>
          )}

          <div
            className={classNames(
              'mc-flex mc-flex-row mc-mt-10',
              `mc-justify-${showAttachmentButton ? 'between' : 'end'}`
            )}
          >
            {showAttachmentButton && (
              <div className="mc-flex mc-items-center">
                <UploadButton
                  onFileSelect={(selectedFiles) => {
                    onFileSelect?.(selectedFiles)
                    let newErrorMessages: string[] = []
                    // Only add files if we haven't reached the maximum
                    const maxFiles = settings.maxFiles || 10
                    if (files.length < maxFiles) {
                      // If multiple files selected, only add up to the maximum
                      const remainingSlots = maxFiles - files.length
                      const filesToAdd = Array.isArray(selectedFiles)
                        ? selectedFiles.slice(0, remainingSlots)
                        : [selectedFiles]

                      // Check file size before adding
                      filesToAdd.forEach((file) => {
                        if (isFileSizeExceeded(file, settings.maxFileSize)) {
                          const msg = t(
                            'MESSAGE_CENTER.CHAT_INPUT.ERROR.FILESIEZE'
                          )
                            .replace('{size}', settings.maxFileSize)
                            .replace('{filename}', file.name)
                          newErrorMessages.push(msg)
                        } else {
                          addFile(file)
                        }
                      })
                    } else {
                      const msg = t(
                        'MESSAGE_CENTER.CHAT_INPUT.ERROR.ATACHMENT_LIMIT'
                      )
                      newErrorMessages.push(msg)
                    }
                    if (newErrorMessages.length > 0) {
                      setErrorMessages(newErrorMessages)
                      setTimeout(() => {
                        setErrorMessages([])
                      }, 10000)
                    } else {
                      setErrorMessages([])
                    }
                  }}
                  accept={`.${settings.supportedFormats.join(',.')}`}
                  multiple={true}
                  maxFiles={settings.maxFiles}
                  aria-label={t('MESSAGE_CENTER.CHAT.UPLOAD.ATTACH_FILES')}
                  disabled={formDisabled}
                >
                  <span className="mc-hidden md:!mc-block">
                    {t('MESSAGE_CENTER.CHAT.UPLOAD.BUTTON_TEXT')}
                  </span>
                </UploadButton>
                <Tooltip
                  content={
                    <TooltipContent
                      maxFiles={settings.maxFiles}
                      supportedFormats={settings.supportedFormats}
                      maxFileSize={settings.maxFileSize}
                    />
                  }
                  className="mc-ml-2"
                >
                  {/*  TODO Add spinner on file upload */}
                  <SVGIcon
                    data-testid="upload-info-icon"
                    name={SVG_NAMES.INFO}
                    width={px2rem(20)}
                    height={px2rem(20)}
                    className="mc-ml-10"
                    role="img"
                    aria-label={t('MESSAGE_CENTER.CHAT_INPUT.TOOLTIP_INFO')}
                  />
                </Tooltip>
              </div>
            )}
            <Button
              onClick={onClick}
              icon={
                formDisabled
                  ? SVG_NAMES.LOADER_DISABLED
                  : disabled || text.length === 0
                    ? SVG_NAMES.SEND_DISABLED
                    : SVG_NAMES.SEND
              }
              disabled={disabled || formDisabled || text.length === 0}
              data-testid="mc-send-button"
              aria-label={t('MESSAGE_CENTER.CHAT.SEND')}
              type="submit"
              iconClassName={formDisabled ? 'mc-animate-spin' : ''}
            >
              {t('MESSAGE_CENTER.CHAT.SEND')}
            </Button>
          </div>
          <div
            data-testid="message-input-error"
            className="mc-mt-10"
            aria-live="assertive"
            role="alert"
          >
            <ul className="mc-list-disc mc-pl-20">
              {errorMessages.map((msg, idx) => (
                <li
                  key={`${msg}-${idx}`}
                  className="mc-text-red-main mc-text-[14px] mc-font-normal mc-leading-[20px] mc-list-none"
                >
                  {msg}
                </li>
              ))}
            </ul>
          </div>
        </fieldset>
      </form>
    )
  }
)
