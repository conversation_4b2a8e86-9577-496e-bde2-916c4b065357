import { FC } from 'react'
import { useTranslation } from 'react-i18next'

interface TooltipContentProps {
  maxFiles: number
  supportedFormats: string[]
  maxFileSize: string
}

export const TooltipContent: FC<TooltipContentProps> = ({
  maxFiles,
  supportedFormats,
  maxFileSize,
}) => {
  const { t } = useTranslation()

  return (
    <>
      {t('MESSAGE_CENTER.CHAT.UPLOAD.MAX_FILES').replace(
        '{count}',
        maxFiles.toString()
      )}
      <br />
      {t('MESSAGE_CENTER.CHAT.UPLOAD.SUPPORTED_FORMATS').replace(
        '{formats}',
        supportedFormats.join(', ')
      )}
      <br />
      {t('MESSAGE_CENTER.CHAT.UPLOAD.MAX_SIZE').replace('{size}', maxFileSize)}
    </>
  )
}
