import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, vi, expect } from 'vitest'
import { ChatInput } from '../ChatInput'
import {
  MessageCenterContext,
  defaultSettings,
} from '../../../contexts/MessageCenterContext'
import { AppVariant } from '../../../types'
import { ReactNode, useRef } from 'react'

// Mock the i18next translation function
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      // Handle the specific case for file size error message
      if (key === 'MESSAGE_CENTER.CHAT_INPUT.ERROR.FILESIEZE') {
        return 'File {filename} exceeds maximum size of {size}'
      }
      return key
    },
  }),
}))

describe('ChatInput Component', () => {
  // Default context wrapper for tests
  const renderWithContext = (
    ui: ReactNode,
    contextValue = { settings: defaultSettings, subjects: [] }
  ) => {
    return render(
      <MessageCenterContext.Provider value={contextValue}>
        {ui}
      </MessageCenterContext.Provider>
    )
  }

  it('renders correctly and matches snapshot', () => {
    const onSubmit = vi.fn()
    const onError = vi.fn()
    const getSignedUrl = vi.fn()
    const { container } = renderWithContext(
      <ChatInput
        onSubmit={onSubmit}
        onError={onError}
        getSignedUrl={getSignedUrl}
      />
    )
    expect(container).toMatchSnapshot()
  })

  it('disables send button when input is empty', () => {
    renderWithContext(
      <ChatInput onSubmit={vi.fn()} onError={vi.fn()} getSignedUrl={vi.fn()} />
    )

    const button = screen.getByTestId('mc-send-button')
    expect(button.hasAttribute('disabled')).toBe(true)
  })

  it('enables send button when text is entered and calls onSend', async () => {
    const onSubmit = vi.fn()
    const getSignedUrl = vi.fn().mockResolvedValue({
      status: 'success',
      data: [],
      message: '',
    })

    renderWithContext(
      <ChatInput
        onSubmit={onSubmit}
        onError={vi.fn()}
        getSignedUrl={getSignedUrl}
      />,
      {
        settings: defaultSettings,
        subjects: [],
      }
    )

    const textarea = screen.getByTestId('mc-textarea')
    const button = screen.getByTestId('mc-send-button')

    fireEvent.change(textarea, { target: { value: 'Hello user' } })
    expect(button).not.toBeDisabled()

    fireEvent.click(button)

    expect(button).toBeDisabled()

    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        content: 'Hello user',
        attachments: undefined,
      })
      expect(button).not.toBeDisabled()
    })
  })

  it('wont send message if getSignedUrl fails', async () => {
    const onSubmit = vi.fn()
    renderWithContext(
      <ChatInput
        disabled={false}
        onSubmit={onSubmit}
        onError={vi.fn()}
        getSignedUrl={vi.fn().mockReturnValue({
          status: 'error',
          data: [],
          message: '',
        })}
        showAttachmentButton
      />
    )

    const textarea = screen.getByTestId('mc-textarea')
    const button = screen.getByTestId('mc-send-button')
    // attach file
    const fileInput = screen.getByTestId('mc-upload-button')
    fireEvent.change(fileInput, {
      target: {
        files: [new File(['test'], 'test.txt', { type: 'text/plain' })],
      },
    })

    // await for file to be displayed
    await waitFor(() => {
      expect(screen.queryByText('test.txt')).not.toBeNull()
    })

    fireEvent.change(textarea, { target: { value: 'Hello' } })
    expect(button.hasAttribute('disabled')).toBe(false)

    fireEvent.click(button)
    await waitFor(() => {
      expect(onSubmit).not.toHaveBeenCalled()
    })
  })

  it('uses default file upload settings when not provided in context', () => {
    // Use the defaultSettings from MessageCenterContext but override language and appVariant
    const partialSettings = {
      ...defaultSettings,
      language: 'en',
      appVariant: AppVariant.Seller,
    }

    renderWithContext(
      <ChatInput
        onSubmit={vi.fn()}
        onError={vi.fn()}
        getSignedUrl={vi.fn()}
        showAttachmentButton
      />,
      {
        settings: partialSettings,
        subjects: [],
      }
    )

    const uploadButton = screen.getByTestId('mc-upload-button')
    expect(uploadButton.getAttribute('accept')).toBe(
      '.pdf,.png,.jpg,.jpeg,.xls,.doc,.docx,.txt'
    )
  })

  it('uses custom file upload settings from context when provided', () => {
    const customSettings = {
      language: 'en',
      appVariant: AppVariant.Seller,
      supportedFormats: ['csv', 'pdf'],
      maxFileSize: '5MB',
      maxFiles: 5,
    }

    renderWithContext(
      <ChatInput
        onSubmit={vi.fn()}
        onError={vi.fn()}
        getSignedUrl={vi.fn()}
        showAttachmentButton
      />,
      {
        settings: customSettings,
        subjects: [],
      }
    )

    const uploadButton = screen.getByTestId('mc-upload-button')
    expect(uploadButton.getAttribute('accept')).toBe('.csv,.pdf')
  })

  it('displays error message when file exceeds maximum size', async () => {
    const onError = vi.fn()
    const customSettings = {
      language: 'en',
      appVariant: AppVariant.Seller,
      supportedFormats: ['txt'],
      maxFileSize: '1KB', // Set a small size for testing
      maxFiles: 5,
    }

    renderWithContext(
      <ChatInput
        onSubmit={vi.fn()}
        onError={onError}
        getSignedUrl={vi.fn()}
        showAttachmentButton
      />,
      {
        settings: customSettings,
        subjects: [],
      }
    )

    // File larger than 1KB
    const largeContent = 'a'.repeat(1025)
    const largeFile = new File([largeContent], 'large-file.txt', {
      type: 'text/plain',
    })

    const fileInput = screen.getByTestId('mc-upload-button')
    fireEvent.change(fileInput, {
      target: {
        files: [largeFile],
      },
    })

    await waitFor(() => {
      const errorContainer = screen.getByTestId('message-input-error')
      expect(errorContainer).toBeInTheDocument()
      expect(errorContainer.textContent).toContain(
        'File large-file.txt exceeds maximum size of 1KB'
      )

      const errorListItems = screen.getAllByRole('listitem')
      expect(errorListItems.length).toBeGreaterThan(0)
      expect(errorListItems[0].textContent).toContain('large-file.txt')
    })
  })

  it('disables the send button while submitting and re-enables it after', async () => {
    const onError = vi.fn()
    const mockSubmit = vi.fn(
      () => new Promise<void>((resolve) => setTimeout(resolve, 100))
    )
    const customSettings = {
      language: 'en',
      appVariant: AppVariant.Seller,
      supportedFormats: ['csv', 'pdf'],
      maxFileSize: '5MB',
      maxFiles: 5,
    }

    renderWithContext(
      <ChatInput
        onSubmit={mockSubmit}
        onError={onError}
        getSignedUrl={vi.fn()}
        showAttachmentButton
      />,
      {
        settings: customSettings,
        subjects: [],
      }
    )

    const textarea = screen.getByTestId('mc-textarea')
    const button = screen.getByTestId('mc-send-button')
    const fieldSet = screen.getByTestId('message-composer')

    fireEvent.change(textarea, { target: { value: 'Hello' } })
    expect(fieldSet.hasAttribute('disabled')).toBe(false)
    fireEvent.click(button)

    expect(fieldSet).toBeDisabled()

    await waitFor(() => {
      expect(fieldSet).not.toBeDisabled()
    })
  })

  it('shows loader icon when submitting and replaces it with send icon after', async () => {
    const mockSubmit = vi.fn(
      () => new Promise<void>((resolve) => setTimeout(resolve, 100))
    )

    renderWithContext(
      <ChatInput
        onSubmit={mockSubmit}
        onError={vi.fn()}
        getSignedUrl={vi.fn()}
        showAttachmentButton
      />,
      {
        settings: defaultSettings,
        subjects: [],
      }
    )

    const textarea = screen.getByTestId('mc-textarea')
    fireEvent.change(textarea, { target: { value: 'Test message' } })

    const sendButton = screen.getByTestId('mc-send-button')
    expect(sendButton).not.toBeDisabled()

    fireEvent.click(sendButton)

    // During submission
    expect(sendButton).toBeDisabled()
    expect(sendButton.querySelector('.mc-animate-spin')).toBeTruthy()

    await waitFor(() => {
      expect(sendButton.querySelector('.mc-animate-spin')).toBeFalsy()
      expect(sendButton).not.toBeDisabled()
    })
  })

  it('disables attachment upload while submitting', async () => {
    const mockSubmit = vi.fn(
      () => new Promise<void>((resolve) => setTimeout(resolve, 100))
    )

    renderWithContext(
      <ChatInput
        onSubmit={mockSubmit}
        onError={vi.fn()}
        getSignedUrl={vi.fn()}
        showAttachmentButton
      />,
      {
        settings: defaultSettings,
        subjects: [],
      }
    )

    const textarea = screen.getByTestId('mc-textarea')
    fireEvent.change(textarea, { target: { value: 'Message' } })

    const sendButton = screen.getByTestId('mc-send-button')
    const uploadButton = screen.getByTestId('mc-upload-button')

    expect(uploadButton).not.toBeDisabled()
    fireEvent.click(sendButton)

    await waitFor(() => {
      expect(uploadButton).toBeDisabled()
    })

    await waitFor(() => {
      expect(uploadButton).not.toBeDisabled()
    })
  })

  it('clears input and files after successful submission', async () => {
    const onSubmit = vi.fn().mockResolvedValue(undefined)

    const TestWrapper = () => {
      const ref = useRef(null)

      return (
        <>
          <ChatInput
            ref={ref}
            onSubmit={async (...args) => {
              await onSubmit(...args)
              ref.current?.clearInput()
            }}
            onError={vi.fn()}
            getSignedUrl={vi.fn().mockResolvedValue({
              status: 'success',
              data: [
                {
                  id: '123',
                  fileId: 'fileId',
                  signedUrl: 'https://mockurl.com',
                },
              ],
            })}
            showAttachmentButton
          />
        </>
      )
    }

    const { getByTestId } = render(<TestWrapper />)

    const textarea = getByTestId('mc-textarea') as HTMLTextAreaElement
    const sendButton = getByTestId('mc-send-button')

    fireEvent.change(textarea, { target: { value: 'New message' } })
    fireEvent.click(sendButton)

    await waitFor(() => {
      expect(textarea.value).toBe('')
    })
  })

  it('calls onFileSelect when a file is selected', async () => {
    const onFileSelect = vi.fn()
    renderWithContext(
      <ChatInput
        onSubmit={vi.fn()}
        onError={vi.fn()}
        getSignedUrl={vi.fn()}
        onFileSelect={onFileSelect}
        showAttachmentButton
      />
    )

    const fileInput = screen.getByTestId('mc-upload-button')
    const file = new File(['test'], 'test.txt', { type: 'text/plain' })
    fireEvent.change(fileInput, {
      target: {
        files: [file],
      },
    })

    await waitFor(() => {
      expect(onFileSelect).toHaveBeenCalled()
    })
  })
})
