import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { TooltipContent } from '../TooltipContent'

// The mock will be automatically used because of the __mocks__ directory

describe('TooltipContent Component', () => {
  const props = {
    maxFiles: 2,
    supportedFormats: ['PDF', 'PNG', 'JPEG', 'JPG'],
    maxFileSize: '4MB',
  }

  it('match snapshot', () => {
    const { container } = render(<TooltipContent {...props} />)
    expect(container).toMatchSnapshot()
  })
})
