import { FC, useState } from 'react'
import { SelectedSubject, Subject } from '@common/types'
import Input from '@common/components/Input/Input'
import { SearchSubject } from '@common/components/ChatSubject/SearchSubject'
import { SubjectUI } from '@common/components/ChatSubject/SubjectUI'
import { useTranslation } from 'react-i18next'
import { useSubject } from '@common/hooks/useSubject'

interface Props {
  onSelectSubject: (subject: SelectedSubject, parentSubject?: Subject) => void
  onSearchSubject?: (input: string) => void
}

export const ChatSubject: FC<Props> = ({
  onSelectSubject,
  onSearchSubject,
}) => {
  const [selectedSubjects, setSelectedSubjects] = useState<Subject[]>([])
  const { translatedSubjects } = useSubject()
  const [parentId, setParentId] = useState<number>()
  const { t } = useTranslation()
  const getParentSubject = () => {
    return translatedSubjects.find((sub) => sub.id === parentId)
  }

  const onClickSubject = (subject: Subject, level: number) => {
    setSelectedSubjects((prevSubjects) => {
      if (level === 0) {
        setParentId(subject.id)
        return [{ ...subject, children: [], isSelected: true }]
      } else if (level === 1) {
        const newSubjects = [
          ...prevSubjects.slice(0, 1),
          { ...subject, isSelected: true },
        ]
        onSelectSubject({ subject: subject.subject }, getParentSubject())
        return newSubjects
      }
      return prevSubjects
    })
  }
  const selectedLabel = selectedSubjects
    .map((item) => item.subjectText)
    .join(' > ')

  const handleSubjectInputChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ): void => {
    setSelectedSubjects((prevSubjects) => {
      const subject: Subject = {
        subjectText: event.target.value,
        subject: 'OTHERS',
      }
      const newSubject = [...prevSubjects.slice(0, 1), { ...subject }]
      onSelectSubject({
        subject: subject.subject,
        customSubject: event.target.value,
      })
      return newSubject
    })
  }

  const isSelected = (subject: Subject): boolean => {
    return selectedSubjects.find((sub) => sub.id === subject.id) !== undefined
  }

  return (
    <div className="mc-w-full mc-min-w-full mc-bg-white-main">
      {selectedLabel && (
        <p
          className="mc-text-black mc-text-lg mc-font-bold mc-mb-16"
          aria-live="polite"
          aria-label={`${t('MESSAGE_CENTER.SELECTED_SUBJECT')}: ${selectedLabel}`}
        >
          {selectedLabel}
        </p>
      )}
      <SearchSubject
        onSearch={onSearchSubject}
        subjects={translatedSubjects}
        onSelectSubject={onClickSubject}
      />
      <div className="mc-rounded-16 mc-border-[1px] mc-border-grey-tint-80 mc-flex mc-flex-row mc-min-h-[250px] mc-mt-16">
        <div className="mc-p-8 mc-flex mc-flex-col mc-flex-1 mc-flex-grow mc-border-r-[1px] mc-border-grey-tint-80">
          <SubjectUI
            subjects={translatedSubjects}
            isSelected={isSelected}
            onClickSubject={onClickSubject}
            level={0}
          />
        </div>
        <div className="mc-flex mc-flex-col mc-flex-1 mc-flex-grow mc-p-8">
          {parentId &&
          translatedSubjects.find((subject) => subject.id === parentId)
            ?.isCustom ? (
            <div data-testid="others-subject">
              <label className="mc-text-tiny">
                <span aria-hidden="true">
                  {t('MESSAGE_CENTER.SUBJECT.CUSTOM.INPUT.PLACEHOLDER')}{' '}
                </span>
                <span className="mc-text-red-main" aria-live="polite">
                  *
                </span>
              </label>
              <Input
                id="others-subject-label"
                onChange={handleSubjectInputChange}
                maxLength={40}
                test-id="subject-other-text"
                ariaLabel={t('MESSAGE_CENTER.SUBJECT.CUSTOM.INPUT.PLACEHOLDER')}
              />
            </div>
          ) : (
            parentId && (
              <SubjectUI
                subjects={
                  translatedSubjects.find((subject) => subject.id === parentId)
                    ?.children || []
                }
                isSelected={isSelected}
                onClickSubject={onClickSubject}
                level={1}
              />
            )
          )}
        </div>
      </div>
    </div>
  )
}
