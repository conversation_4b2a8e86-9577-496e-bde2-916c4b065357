import { FC, useState } from 'react'
import SearchInput from '@common/components/SearchInput/SearchInput'
import { Subject } from '@common/types'
import { useTranslation } from 'react-i18next'

interface Props {
  onSelectSubject: (subject: Subject, level: number) => void
  onSearch?: (input: string) => void
  subjects: Subject[]
}

export const SearchSubject: FC<Props> = ({
  onSelectSubject,
  onSearch,
  subjects,
}) => {
  const [query, setQuery] = useState('')
  const [filteredResults, setFilteredResults] = useState<
    { parent?: Subject; child?: Subject }[]
  >([])
  const { t } = useTranslation()

  const handleSearch = (input: string) => {
    setQuery(input)
    onSearch?.(input)
    if (!input.trim()) {
      setFilteredResults([])
      return
    }
    let results: { parent?: Subject; child?: Subject }[] = []

    subjects.forEach((parent) => {
      const parentMatched = (parent?.subjectText || '')
        .toLowerCase()
        .includes(input.toLowerCase())
      let childrenMatched =
        parent.children?.filter((child) =>
          (child?.subjectText || '').toLowerCase().includes(input.toLowerCase())
        ) || []

      if (parentMatched) results.push({ parent })
      childrenMatched.forEach((child) => results.push({ parent, child }))
    })
    setFilteredResults(results)
  }

  const highlightMatch = (text: string, query: string) => {
    if (!query) {
      return text
    }
    const regex = new RegExp(`(${query})`, 'gi')
    return text.replace(regex, '<strong>$1</strong>')
  }

  return (
    <div className="mc-w-full mc-relative">
      <div className="">
        <label className="mc-font-bold mc-block mc-mb-[8px]" aria-hidden="true">
          {t('MESSAGE_CENTER.SUBJECT.CHOOSE.TOPIC')}
        </label>
        <div className="mc-w-full md:mc-w-3/4">
          <SearchInput
            id="search-input-label"
            onChange={handleSearch}
            placeholder={t('MESSAGE_CENTER.SUBJECT.SEARCH.INPUT.PLACEHOLDER')}
            ariaLabel={t('MESSAGE_CENTER.SUBJECT.CHOOSE.TOPIC')}
            ariaRole={t('MESSAGE_CENTER.CHAT_SUBJECT_SEARCH.ARIA_ROLE')}
          />
        </div>
      </div>

      {filteredResults.length > 0 && (
        <ul className="mc-shadow-searchSubjectResult mc-bg-white-main mc-absolute mc-w-full">
          {filteredResults.map(({ parent, child }, index) => (
            <li key={index} className="mc-p-10 mc-cursor-pointer">
              <button
                onClick={() => {
                  if (child && parent) {
                    onSelectSubject(parent, 0)
                    onSelectSubject(child, 1)
                    setQuery('')
                  }
                  if (parent?.subject === 'OTHERS') {
                    onSelectSubject(parent, 0)
                    setQuery('')
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    if (child && parent) {
                      onSelectSubject(parent, 0)
                      onSelectSubject(child, 1)
                      setQuery('')
                    }
                  }
                }}
                aria-label={
                  child
                    ? `${parent!.subjectText} > ${child!.subjectText}`
                    : parent!.subjectText
                }
                className="mc-p-10 mc-cursor-pointer mc-w-full text-left"
              >
                <span
                  aria-hidden="true"
                  dangerouslySetInnerHTML={{
                    __html: child
                      ? `${highlightMatch(parent!.subjectText || '', query)} > ${highlightMatch(child!.subjectText || '', query)}`
                      : highlightMatch(parent!.subjectText || '', query),
                  }}
                ></span>
              </button>
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}
