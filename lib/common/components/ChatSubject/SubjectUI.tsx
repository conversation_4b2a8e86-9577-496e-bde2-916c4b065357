import { FC } from 'react'
import { Subject } from '@common/types'
import classNames from 'classnames'
import { SVGIcon } from '@common/components/SVGIcon/SVGIcon'
import { SVG_NAMES } from '@common/icons'
import { px2rem } from '@common/utils/px2rem'

interface Props {
  subjects: Subject[]
  isSelected: (subject: Subject) => boolean
  onClickSubject: (subject: Subject, level: number) => void
  level: number
}

export const SubjectUI: FC<Props> = ({
  subjects,
  isSelected,
  onClickSubject,
  level,
}) => {
  return (
    <ul className="">
      {subjects.map((subject) => (
        <li key={subject.id}>
          <button
            className={classNames(
              'mc-py-8 mc-px-16 mc-rounded-8 mc-flex-row mc-flex mc-place-content-between mc-items-center mc-w-full mc-text-left',
              {
                'mc-subject-active subject-active mc-border-l-secondary-blue-main mc-border-l-8':
                  isSelected(subject),
              }
            )}
            data-testid={`subject-${subject?.subject?.toLowerCase()}`}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                onClickSubject(subject, level)
              }
            }}
            onClick={() => onClickSubject(subject, level)}
          >
            <span>{subject.subjectText}</span>
            {level < 1 && (
              <SVGIcon
                dataTestid="subject-arrow"
                name={SVG_NAMES.SUBJECT_ARROW}
                width={px2rem(9)}
                height={px2rem(12)}
                className=""
                fill={isSelected(subject) ? '#33435B' : '#DDDDDD'}
              />
            )}
          </button>
        </li>
      ))}
    </ul>
  )
}
