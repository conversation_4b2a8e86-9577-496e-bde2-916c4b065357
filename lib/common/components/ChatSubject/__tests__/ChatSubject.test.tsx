import { render, screen, fireEvent, within } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { ChatSubject } from '../ChatSubject'
import { subjects } from '../../../mocks/subjects'

vi.mock('@common/hooks/useSubject', () => ({
  useSubject: () => ({
    getSubject: (chat) => chat.subject,
    translatedSubjects: subjects,
  }),
}))

describe('ChatSubject Component', () => {
  it('renders correctly and matches snapshot', () => {
    const { container } = render(<ChatSubject onSelectSubject={vi.fn()} />)
    expect(container).toMatchSnapshot()
  })

  it('updates selected subjects on click', () => {
    const mockOnSelect = vi.fn()
    render(<ChatSubject onSelectSubject={mockOnSelect} />)
    const orderSubject = screen.getByTestId('subject-orders')
    fireEvent.click(orderSubject)
    expect(
      screen.getByText('MESSAGE_CENTER.SUBJECT.ORDER_CONFIRMATION')
    ).toBeInTheDocument()
    const orderConfirmationSubject = screen.getByTestId(
      'subject-order_confirmation'
    )
    fireEvent.click(orderConfirmationSubject)
    expect(mockOnSelect).toHaveBeenCalled()
  })

  it('updates the breadcrumb on selecting a subject', () => {
    const mockOnSelect = vi.fn()
    render(<ChatSubject onSelectSubject={mockOnSelect} />)
    const orderSubject = screen.getByTestId('subject-orders')
    fireEvent.click(orderSubject)
    fireEvent.click(screen.getByTestId('subject-order_confirmation'))
    expect(
      screen.getByText(
        'MESSAGE_CENTER.SUBJECT.ORDERS > MESSAGE_CENTER.SUBJECT.ORDER_CONFIRMATION'
      )
    ).toBeInTheDocument()
  })

  it('handles user input when selecting a custom subject', () => {
    const mockOnSelect = vi.fn()
    render(<ChatSubject onSelectSubject={mockOnSelect} />)

    fireEvent.click(screen.getByTestId('subject-others'))
    const input = within(screen.getByTestId('others-subject')).getByTestId(
      'mc-input'
    )

    fireEvent.change(input, { target: { value: 'Where is my order' } })
    expect(mockOnSelect).toHaveBeenCalled()
  })

  it('does not call onSelectSubject when a parent subject is clicked', () => {
    const mockOnSelect = vi.fn()
    render(<ChatSubject onSelectSubject={mockOnSelect} />)
    const orderSubject = screen.getByTestId('subject-orders')
    fireEvent.click(orderSubject)
    expect(mockOnSelect).not.toHaveBeenCalled()
  })

  it('calls onSelectSubject with child and parent subject when a child is clicked', () => {
    const mockOnSelect = vi.fn()
    render(<ChatSubject onSelectSubject={mockOnSelect} />)
    const orderSubject = screen.getByTestId('subject-orders')
    fireEvent.click(orderSubject)
    const orderConfirmationSubject = screen.getByTestId(
      'subject-order_confirmation'
    )
    fireEvent.click(orderConfirmationSubject)
    expect(mockOnSelect).toHaveBeenCalledWith(
      expect.objectContaining({ subject: 'ORDER_CONFIRMATION' }),
      expect.objectContaining({ subject: 'ORDERS' })
    )
  })
})
