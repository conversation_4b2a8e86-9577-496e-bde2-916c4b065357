import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import '@testing-library/jest-dom'
import { SearchSubject } from '../SearchSubject'
import { subjects } from '../../../mocks/subjects'

// Mock react-i18next
vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
  }),
}))

describe('SearchSubject Component', () => {
  it('renders correctly and matches snapshot', () => {
    const { container } = render(
      <SearchSubject subjects={subjects} onSelectSubject={vi.fn()} />
    )
    expect(container).toMatchSnapshot()
  })

  it('filters subjects based on query', async () => {
    const onSelectSubject = vi.fn()
    render(
      <SearchSubject onSelectSubject={onSelectSubject} subjects={subjects} />
    )
    const input = screen.getByPlaceholderText(
      /MESSAGE_CENTER.SUBJECT.SEARCH.INPUT.PLACEHOLDER/i
    )

    fireEvent.change(input, { target: { value: 'order' } })

    await waitFor(() => {
      // Check for elements by their aria-label instead of text content since text is split by highlighting
      expect(
        screen.getByLabelText('MESSAGE_CENTER.SUBJECT.ORDERS')
      ).toBeInTheDocument()
      expect(
        screen.getByLabelText(
          'MESSAGE_CENTER.SUBJECT.ORDERS > MESSAGE_CENTER.SUBJECT.ORDER_CONFIRMATION'
        )
      ).toBeInTheDocument()
      expect(
        screen.getByLabelText(
          'MESSAGE_CENTER.SUBJECT.ORDERS > MESSAGE_CENTER.SUBJECT.ORDER_TRACKING'
        )
      ).toBeInTheDocument()
    })
  })

  it('calls onSelectSubject when a subject is clicked', async () => {
    const mockOnSelect = vi.fn()
    render(<SearchSubject subjects={subjects} onSelectSubject={mockOnSelect} />)
    const input = screen.getByPlaceholderText(
      /MESSAGE_CENTER.SUBJECT.SEARCH.INPUT.PLACEHOLDER/i
    )

    fireEvent.change(input, { target: { value: 'order' } })

    await waitFor(() => {
      const orderConfirmation = screen.getByLabelText(
        'MESSAGE_CENTER.SUBJECT.ORDERS > MESSAGE_CENTER.SUBJECT.ORDER_CONFIRMATION'
      )
      fireEvent.click(orderConfirmation)
    })

    expect(mockOnSelect).toHaveBeenCalledTimes(2)
  })
})
