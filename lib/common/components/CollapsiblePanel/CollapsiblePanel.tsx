import { FC, ReactElement, useEffect, useId, useRef, useState } from 'react'
import { SVGIcon } from '@common/components/SVGIcon/SVGIcon'
import { SVG_NAMES } from '@common/icons'
import classNames from 'classnames'

interface Props {
  title: string
  children: ReactElement
  isExpanded?: boolean
}

export const CollapsiblePanel: FC<Props> = ({
  title,
  children,
  isExpanded = false,
}: Props) => {
  const [isOpen, setIsOpen] = useState(isExpanded)
  const [height, setHeight] = useState<string | number>('0px')
  const contentRef = useRef<HTMLDivElement>(null)
  useEffect(() => {
    if (contentRef.current) {
      setHeight(isOpen ? contentRef.current.scrollHeight : '0px')
    }
  }, [isOpen])
  const panelId = useId()

  const togglePanel = () => setIsOpen(!isOpen)

  return (
    <div className="mc-rounded-16 mc-p-20 mc-bg-white-main">
      <button
        aria-expanded={isOpen}
        aria-controls={panelId}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            togglePanel()
          }
        }}
        className="mc-flex mc-justify-between mc-items-center mc-w-full"
        onClick={togglePanel}
      >
        <h2
          className="!mc-font-lato mc-text-xl mc-font-bold mc-text-metro-blue-main"
          data-testid="panel-title"
        >
          {title}
        </h2>
        <SVGIcon
          name={SVG_NAMES.COLLAPSE_BUTTON}
          className={classNames(
            'mc-transition-transform mc-duration-300 mc-w-[16px] mc-h-[16px]',
            {
              'mc-rotate-180': isOpen,
              'mc-rotate-0': !isOpen,
            }
          )}
        />
      </button>
      <div
        id={panelId}
        ref={contentRef}
        style={{
          maxHeight: height,
        }}
        aria-hidden={!isOpen}
        className="mc-overflow-hidden mc-transition-all mc-duration-300 mc-ease-linear"
        data-testid="panel-content"
      >
        <div className="mc-pt-20">{children}</div>
      </div>
    </div>
  )
}
