import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { CollapsiblePanel } from '../CollapsiblePanel'

describe('CollapsiblePanel', () => {
  const panelTitle = 'Test Panel'
  const panelContent = <p>Hidden content</p>

  beforeEach(() => {
    vi.useFakeTimers()
  })

  it('renders title and hides content by default', () => {
    render(
      <CollapsiblePanel title={panelTitle}>{panelContent}</CollapsiblePanel>
    )

    expect(
      screen.getByRole('button', { name: /test panel/i })
    ).toBeInTheDocument()
    const contentWrapper = screen.getByTestId('panel-content') as HTMLDivElement
    expect(contentWrapper).toHaveStyle('max-height: 0px')
  })

  it('renders expanded panel if isExpanded=true', () => {
    render(
      <CollapsiblePanel title={panelTitle} isExpanded>
        {panelContent}
      </CollapsiblePanel>
    )

    expect(screen.getByText(/hidden content/i)).toBeVisible()
    expect(screen.getByRole('button')).toHaveAttribute('aria-expanded', 'true')
  })

  it('toggles panel visibility on click', () => {
    render(
      <CollapsiblePanel title={panelTitle}>{panelContent}</CollapsiblePanel>
    )
    const toggleButton = screen.getByRole('button')
    const contentWrapper = screen.getByTestId('panel-content') as HTMLDivElement

    fireEvent.click(toggleButton)
    vi.runAllTimers()
    expect(contentWrapper).not.toHaveStyle('max-height: 0px')

    fireEvent.click(toggleButton)
    vi.runAllTimers()
    expect(contentWrapper).toHaveStyle('max-height: 0px')
  })

  it('responds to keyboard interaction (Enter and Space)', () => {
    render(
      <CollapsiblePanel title={panelTitle}>{panelContent}</CollapsiblePanel>
    )
    const toggleButton = screen.getByRole('button')
    const contentWrapper = screen.getByTestId('panel-content') as HTMLDivElement

    fireEvent.keyDown(toggleButton, { key: 'Enter' })
    vi.runAllTimers()
    expect(contentWrapper).not.toHaveStyle('max-height: 0px')

    fireEvent.keyDown(toggleButton, { key: ' ' })
    vi.runAllTimers()
    expect(contentWrapper).toHaveStyle('max-height: 0px')
  })
})
