import { render, screen } from '@testing-library/react'
import { Container } from '../Container'

describe('Container', () => {
  it('renders children correctly', () => {
    // Given
    const childText = 'Test Child'

    // When
    render(
      <Container>
        <div>{childText}</div>
      </Container>
    )

    // Then
    expect(screen.getByText(childText)).toBeInTheDocument()
  })

  it('should render with default "div" element and correct class names', () => {
    render(
      <Container>
        <p>Test Child</p>
      </Container>
    )

    const container = screen.getByText('Test Child').parentElement

    expect(container).toHaveClass(
      'mc-rounded-16 mc-bg-white-main mc-h-full mc-box-border mc-p-8 mc-font-lato mc-w-full'
    )
    expect(container).toBeInTheDocument()
    expect(container).toBeInstanceOf(HTMLDivElement)
  })

  it('should render with a different component if "as" prop is provided', () => {
    render(
      <Container as="section">
        <p>Test Child</p>
      </Container>
    )

    const container = screen.getByText('Test Child').parentElement

    expect(container).toBeInTheDocument()
    expect(container?.tagName).toBe('SECTION')
  })

  it('should render children correctly', () => {
    render(
      <Container>
        <p>Child Text</p>
      </Container>
    )

    expect(screen.getByText('Child Text')).toBeInTheDocument()
  })
})
