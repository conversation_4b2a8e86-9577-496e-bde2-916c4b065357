import classNames from 'classnames'
import { FC } from 'react'

type Props = React.InputHTMLAttributes<HTMLInputElement> & {
  className?: string
  invalid?: boolean
  ariaLabel?: string
}

const Input: FC<Props> = ({
  className,
  invalid,
  disabled,
  type = 'text',
  ariaLabel,
  id,
  ...rest
}) => {
  const inputClassName = classNames(
    'mc-rounded-6 mc-border-[1px] mc-border-grey-tint-80',
    'mc-outline-none mc-py-8 mc-px-12 mc-w-full mc-h-40',
    'mc-outline-none placeholder:mc-italic mc-placeholder-grey mc-bg-white-main',
    'focus:mc-border-bue-tint-40',
    className,
    {
      'mc-border-[1px] mc-border-metro-red': invalid,
      'mc-bg-grey-tint-80': disabled,
    }
  )

  return (
    <>
      <label htmlFor={id} className="mc-sr-only">
        {ariaLabel}
      </label>
      <input
        id={id}
        type={type}
        {...rest}
        disabled={disabled}
        data-testid="mc-input"
        className={inputClassName}
      />
    </>
  )
}

export default Input
