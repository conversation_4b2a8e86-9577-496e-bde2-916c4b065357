import { FC } from 'react'
import { Attachment, Message } from '@common/types'
import { SVGIcon } from '@common/components/SVGIcon/SVGIcon'
import { SVG_NAMES } from '@common/icons'
import { px2rem } from '@common/utils/px2rem'
import classNames from 'classnames'
import { useMessageUserRoles } from '@common/hooks/useMessageUserRoles'
import { useTranslation } from 'react-i18next'

interface Props {
  attachments: Attachment[]
  message: Message
}

export const AttachmentBubble: FC<Props> = ({ attachments, message }) => {
  const { isReceiver } = useMessageUserRoles(message)
  const { t } = useTranslation()
  if (!attachments || attachments.length === 0) {
    return null
  }

  return (
    <article
      data-testid="attachment-container"
      className={classNames('mc-mt-20 mc-flex mc-w-full', {
        'mc-justify-end': isReceiver,
        'mc-justify-start': !isReceiver,
      })}
      aria-label={t('MESSAGE_CENTER.MESSAGE_ATTACHMENTS')}
      role="group"
    >
      <div className="mc-p-5 mc-shadow-md mc-max-w-[80%] mc-rounded-[16px] mc-bg-white-main">
        {attachments.map((attachment, index) => (
          <div
            key={attachment.fileId}
            className={`mc-flex mc-items-center mc-px-8 mc-py-4 ${index < attachments.length - 1 ? 'mc-mb-3' : ''}`}
          >
            <a
              href={`${attachment.downloadUrl}${attachment.downloadUrl?.includes('?') ? '&' : '?'}response-content-disposition=attachment`}
              rel="noopener noreferrer"
              className="mc-flex mc-items-center"
              download
              aria-label={`${t('MESSAGE_CENTER.DOWNLOAD_ATTACHMENT')} ${attachment.fileName}`}
            >
              <SVGIcon
                name={SVG_NAMES.DOWNLOAD}
                fill="#0059e4"
                width={px2rem(16)}
                height={px2rem(16)}
                className="mc-cursor-pointer mc-text-grey-tint-40 hover:mc-text-grey-tint-20"
                dataTestid="download-file-icon"
                aria-hidden="true"
              />
              <p className="mc-font-lato mc-text-[14px] mc-font-normal mc-leading-[20px] mc-text-secondary-blue-main mc-underline mc-pl-12 mc-truncate mc-max-w-[200px]">
                {attachment.fileName}
              </p>
            </a>
          </div>
        ))}
      </div>
    </article>
  )
}
