import { forwardRef } from 'react'
import { AppVariant, Chat, Message } from '@common/types'
import { parseDate, timeFormat } from '@common/utils/date.util'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import classNames from 'classnames'
import { useMessageUserRoles } from '@common/hooks/useMessageUserRoles'
import { AttachmentBubble } from './AttachmentBubble'
import { useTranslation } from 'react-i18next'

interface Props {
  message: Message
  chat?: Chat
}

export const ChatBubble = forwardRef<HTMLDivElement, Props>(
  ({ message, chat }, ref) => {
    const { t } = useTranslation()
    const { settings } = useMessageCenterContext()
    const { isReceiver, isSeller } = useMessageUserRoles(message)
    const arrowClass = classNames(
      'mc-absolute mc-absolute mc-block mc-w-0 mc-bottom-[-16px] ',
      {
        'mc-border-t-[16px] mc-border-r-[16px] mc-border-b-0 mc-border-l-0 mc-border-solid mc-border-t-white-main mc-border-b-white-main mc-border-r-transparent mc-border-l-transparent mc-w-0 mc--bottom-4 mc-left-0':
          !isReceiver,
        'mc-border-t-[16px] mc-border-l-[16px] mc-border-solid mc-border-t-blue-tint-80 mc-border-b-blue-tint-80 mc-border-r-transparent mc-border-l-transparent mc-block mc-w-0 mc--bottom-4 mc-right-0':
          isReceiver,
      }
    )
    const senderLabel = isReceiver
      ? t('MESSAGE_CENTER.YOU')
      : isSeller
        ? `${chat?.seller.organization.shopName ?? ''}`
        : `${chat?.buyer.firstName ?? ''} ${chat?.buyer.lastName ?? ''}`
    const dateLabel = parseDate(message.createdAt, 'DD MMM YYYY')
    const timeLabel = parseDate(message.createdAt, 'hh:mm')

    return (
      <article
        ref={ref}
        className={classNames('mc-flex mc-flex-col mc-mb-16', {
          'mc-items-end': isReceiver,
          'mc-items-start': !isReceiver,
        })}
        aria-label={`${t('MESSAGE_CENTER.MESSAGE_FROM').replace('{sender}', senderLabel).replace('{date}', dateLabel).replace('{time}', timeLabel)}`}
        data-testid={`message-${message.id}`}
      >
        <div className="mc-w-[95%]">
          <time
            className="mc-text-base"
            dateTime={new Date(message.createdAt).toISOString()}
            aria-hidden="true"
          >
            {parseDate(message.createdAt)}
          </time>
          <div
            className={classNames('mc-relative ', {
              'mc-rounded-[16px_16px_16px_0] mc-p-10 mc-bg-white-main':
                !isReceiver,
              'mc-rounded-[16px_16px_0_16px] mc-p-10 mc-bg-blue-tint-80':
                isReceiver,
            })}
          >
            {settings.appVariant === AppVariant.Employee && (
              <div className="mc-mb-10">
                <span className="mc-text-primary-main mc-text-base">
                  {isSeller
                    ? `Seller: ${chat?.seller.organization.shopName}`
                    : `Buyer: ${chat?.buyer.firstName} ${chat?.buyer.lastName}`}
                </span>
              </div>
            )}

            <p
              className="mc-mb-10 mc-text-regular mc-whitespace-pre-wrap mc-break-all"
              data-testid={`message-content-${message.id}`}
            >
              {message.content}
            </p>
            <p className="mc-text-right mc-text-base" aria-hidden="true">
              {parseDate(message.createdAt, timeFormat)}
            </p>
            <span className={arrowClass}></span>
          </div>
          {message.attachments && message.attachments.length > 0 && (
            <AttachmentBubble
              attachments={message.attachments}
              message={message}
            />
          )}
        </div>
      </article>
    )
  }
)
