import { FC, useEffect, useRef } from 'react'
import { AppVariant, Chat, Message } from '@common/types'
import { MessageHistoryHeader } from '@common/components/MessageHistory/MessageHistoryHeader'
import { ChatBubble } from '@common/components/MessageHistory/ChatBubble'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import classNames from 'classnames'

interface Props {
  messages: Message[]
  chat: Chat
  children?: JSX.Element
}

export const MessageHistory: FC<Props> = ({ messages, chat, children }) => {
  const { settings } = useMessageCenterContext()
  const chatRef = useRef<HTMLDivElement>(null)
  const lastMessageRef = useRef<HTMLDivElement | null>(null)
  const containerClass = classNames(
    'mc-bg-white-main mc-w-full mc-h-full mc-flex mc-flex-col',
    settings.appVariant === AppVariant.Employee ? 'mc-pb-10' : 'mc-pb-20'
  )

  useEffect(() => {
    if (chatRef?.current && lastMessageRef?.current) {
      const containerTop = chatRef.current.getBoundingClientRect().top
      const messageTop = lastMessageRef.current.getBoundingClientRect().top

      chatRef.current.scrollTop =
        messageTop - containerTop + chatRef.current.scrollTop
    }
  }, [messages])
  return (
    <section className={containerClass}>
      <div className="mc-sticky mc-top-0 mc-z-5 mc-bg-white-main">
        {children}
        <MessageHistoryHeader
          data-testid="message-history-header"
          chat={chat}
        />
      </div>
      <div
        className="mc-bg-grey-tint-95 mc-rounded-16 mc-p-[16px_0_16px_16px] mc-overflow-y-scroll mc-flex-1 mc-scroll-smooth"
        role="log"
        aria-live="polite"
        aria-relevant="additions"
        ref={chatRef}
        tabIndex={-1}
      >
        <ul className="mc-pr-16 mc-min-h-full mc-flex mc-justify-end mc-flex-col">
          {messages.map((message, index) => (
            <li key={message.id}>
              <ChatBubble
                ref={index === messages.length - 1 ? lastMessageRef : null}
                message={message}
                chat={chat}
              />
            </li>
          ))}
        </ul>
      </div>
    </section>
  )
}
