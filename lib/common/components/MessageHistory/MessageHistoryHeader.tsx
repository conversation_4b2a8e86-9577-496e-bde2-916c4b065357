import { AppVariant, Chat } from '@common/types'
import { FC } from 'react'
import { parseDate } from '@common/utils/date.util'
import { useSubject } from '@common/hooks/useSubject'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import { useTranslation } from 'react-i18next'
import { ChatStatusBadges } from '@common/components/Badge/ChatStatusBadges'

interface Props {
  chat: Chat
}

export const MessageHistoryHeader: FC<Props> = ({ chat }) => {
  const { settings } = useMessageCenterContext()
  const { getSubject } = useSubject()
  const { t } = useTranslation()
  const subject = getSubject(chat)

  return (
    <header className="mc-mb-20 mc-font-bold" role="presentation">
      <span className="mc-sr-only">{t('MESSAGE_CENTER.CHAT.DETAILS')}</span>
      <div className="mc-flex mc-justify-between mc-mb-20">
        <div className="mc-flex mc-items-center mc-gap-2">
          <ChatStatusBadges chat={chat} variant={settings.appVariant} />
        </div>
        <time
          dateTime={new Date(chat.createdAt).toISOString()}
          aria-label={`${t('MESSAGE_CENTER.CHAT_CREATED_ON')} ${parseDate(chat.createdAt)}`}
        >
          {parseDate(chat.createdAt)}
        </time>
      </div>
      <div className="mc-text-metro-blue-main">
        {settings.appVariant === AppVariant.Employee && (
          <>
            <h2 className="mc-text-2lg">
              Buyer: {chat.buyer?.firstName} {chat.buyer?.lastName}
            </h2>
            <h2 className="mc-text-2lg mc-mb-16">
              Seller: {chat.seller?.organization.shopName}
            </h2>
            <p className="mc-text-lg">{chat.order.orderNumber}</p>
          </>
        )}
        {settings.appVariant === AppVariant.Buyer && (
          <>
            <h2
              className="mc-text-2lg mc-mb-16"
              aria-label={`${t('MESSAGE_CENTER.SELLER_NAME')} ${chat.seller.organization.shopName ?? ''}`}
            >
              {chat.seller.organization.shopName ?? ''}
            </h2>
            <h3
              className="mc-text-lg"
              aria-label={`${t('MESSAGE_CENTER.ORDER_NUMBER')}: ${chat.order.orderNumber}`}
            >
              {chat.order.orderNumber}
            </h3>
          </>
        )}
        {settings.appVariant === AppVariant.Seller && (
          <>
            <p className="mc-text-2lg mc-mb-16">
              {chat.buyer.firstName ?? ''} {chat.buyer.lastName ?? ''}
            </p>
            <p className="mc-text-lg">{chat.order.orderNumber}</p>
          </>
        )}

        {subject && (
          <p
            className="mc-text-lg mc-mt-8"
            aria-label={`${t('MESSAGE_CENTER.SUBJECT')}: ${subject}`}
          >
            {subject}
          </p>
        )}
      </div>
    </header>
  )
}
