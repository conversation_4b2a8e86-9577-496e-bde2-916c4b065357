import { render, screen } from '@testing-library/react'
import { AttachmentBubble } from '../AttachmentBubble'
import { Attachment, Message } from '@common/types'
import { vi } from 'vitest'

// Mock the useMessageUserRoles hook
vi.mock('@common/hooks/useMessageUserRoles', () => ({
  useMessageUserRoles: (message: Message) => ({
    isReceiver: message.senderUserType === 'BUYER',
    isSeller: message.senderUserType === 'SELLER',
  }),
}))

describe('AttachmentBubble Component', () => {
  const mockMessage: Message = {
    id: 'test-message-id',
    senderId: 'sender-id',
    senderUserType: 'SELLER',
    content: 'Test message',
    createdAt: '2025-04-08T09:21:55+00:00',
  }

  const mockReceiverMessage: Message = {
    ...mockMessage,
    id: 'receiver-message-id',
    senderUserType: 'BUYER',
  }

  const mockAttachments: Attachment[] = [
    {
      fileId: 'files/ec82fc10-102b-4e57-a599-0534c160efd3/file1',
      fileType: 'image/png',
      fileName: 'image1.png',
      downloadUrl: 'https://example.com/download/image1.png',
    },
    {
      fileId: 'files/ec82fc10-102b-4e57-a599-0534c160efd3/file2',
      fileType: 'application/pdf',
      fileName: 'document.pdf',
      downloadUrl: 'https://example.com/download/document.pdf',
    },
  ]

  it('renders nothing when attachments array is empty', () => {
    const { container } = render(
      <AttachmentBubble attachments={[]} message={mockMessage} />
    )
    expect(container.firstChild).toBeNull()
  })

  it('renders nothing when attachments is undefined', () => {
    // @ts-ignore - Testing undefined case
    const { container } = render(
      <AttachmentBubble attachments={undefined} message={mockMessage} />
    )
    expect(container.firstChild).toBeNull()
  })

  it('renders all attachments with download icons', () => {
    render(
      <AttachmentBubble attachments={mockAttachments} message={mockMessage} />
    )

    // Check if all file names are displayed
    expect(screen.getByText('image1.png')).toBeInTheDocument()
    expect(screen.getByText('document.pdf')).toBeInTheDocument()

    // Check if download icons are present
    const downloadIcons = screen.getAllByTestId('download-file-icon')
    expect(downloadIcons).toHaveLength(2)

    // Check if links have correct href attributes
    const links = screen.getAllByRole('link')
    expect(links[0]).toHaveAttribute(
      'href',
      'https://example.com/download/image1.png?response-content-disposition=attachment'
    )
    expect(links[1]).toHaveAttribute(
      'href',
      'https://example.com/download/document.pdf?response-content-disposition=attachment'
    )
  })

  it('applies correct styling based on sender type', () => {
    const { rerender } = render(
      <AttachmentBubble attachments={mockAttachments} message={mockMessage} />
    )

    // Check styling for sender (SELLER)
    let container = screen.getByTestId('attachment-container')
    expect(container.firstChild).toHaveClass('mc-rounded-[16px]')
    expect(container.firstChild).toHaveClass('mc-bg-white-main')

    // Rerender with receiver message (BUYER)
    rerender(
      <AttachmentBubble
        attachments={mockAttachments}
        message={mockReceiverMessage}
      />
    )

    // Check styling for receiver
    container = screen.getByTestId('attachment-container')
    expect(container.firstChild).toHaveClass('mc-rounded-[16px]')
    expect(container.firstChild).toHaveClass('mc-bg-white-main')
  })
})
