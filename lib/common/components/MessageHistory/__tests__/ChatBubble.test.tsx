import { render, screen } from '@testing-library/react'
import { mockMessages } from '../../../mocks/messages'
import { ChatBubble } from '../ChatBubble'
import { parseDate, timeFormat } from '../../../utils/date.util'
import { AppVariant, Attachment, Message } from '../../../types'
import { mockEmployeeChatList } from '../../../mocks/employee-chatlist'

vi.mock('@common/components/MessageHistory/MessageHistoryHeader', () => ({
  MessageHistoryHeader: vi.fn(() => (
    <div data-testid="message-history-header" />
  )),
}))

vi.mock('../AttachmentBubble', () => ({
  AttachmentBubble: ({ attachments, message }) => (
    <div data-testid="attachment-bubble">
      {attachments.map((attachment: Attachment) => (
        <div key={attachment.fileId} data-testid="attachment-item">
          {attachment.fileName}
        </div>
      ))}
    </div>
  ),
}))

let appVariantMock = AppVariant.Seller
vi.mock('@common/contexts/MessageCenterContext', async () => {
  const actual = await import('@common/contexts/MessageCenterContext')
  return {
    MessageCenterContext: actual.MessageCenterContext,
    useMessageCenterContext: () => ({
      settings: { appVariant: appVariantMock },
    }),
  }
})

describe('ChatBubble Component', () => {
  beforeEach(() => {
    vi.resetModules()
  })

  const mockMessageWithAttachments: Message = {
    ...mockMessages[0],
    id: 'message-with-attachments',
    attachments: [
      {
        fileId: 'file-1',
        fileName: 'test-file.pdf',
        fileType: 'application/pdf',
        downloadUrl: 'https://example.com/test-file.pdf',
      },
      {
        fileId: 'file-2',
        fileName: 'image.png',
        fileType: 'image/png',
        downloadUrl: 'https://example.com/image.png',
      },
    ],
  }

  it('renders message content and timestamps with correct formatting', () => {
    const message = mockMessages[0]
    const expectedDate = parseDate(message.createdAt)
    const expectedTime = parseDate(message.createdAt, timeFormat)

    render(<ChatBubble message={message} />)

    expect(screen.getByText(message.content)).toBeInTheDocument()
    expect(screen.getByText(expectedDate)).toBeInTheDocument()
    expect(screen.getByText(expectedTime)).toBeInTheDocument()
  })

  it('applies correct styling based on sender type', () => {
    const message = mockMessages.find(
      (mock) => mock.senderUserType.toLowerCase() === 'buyer'
    )
    render(<ChatBubble message={message} />)
    const bubble = screen.getByText(message.content)?.parentElement
    expect(bubble).toHaveClass('mc-bg-white-main')
  })

  it('displays seller name for Employee app variant', () => {
    const message = mockMessages.find(
      (mock) => mock.senderUserType.toLowerCase() === 'seller'
    )
    const employeeChat = mockEmployeeChatList[0]
    appVariantMock = AppVariant.Employee
    render(<ChatBubble message={message} chat={employeeChat} />)
    expect(
      screen.getByText(`Seller: ${employeeChat.seller.organization.shopName}`)
    ).toBeInTheDocument()
  })

  it('displays buyer name for Employee app variant', () => {
    const message = mockMessages.find(
      (mock) => mock.senderUserType.toLowerCase() === 'buyer'
    )
    const employeeChat = mockEmployeeChatList[0]
    appVariantMock = AppVariant.Employee
    render(<ChatBubble message={message} chat={employeeChat} />)
    expect(
      screen.getByText(
        `Buyer: ${employeeChat.buyer.firstName} ${employeeChat.buyer.lastName}`
      )
    ).toBeInTheDocument()
  })

  it('renders attachments when message has attachments', () => {
    render(<ChatBubble message={mockMessageWithAttachments} />)
    expect(screen.getByTestId('attachment-bubble')).toBeInTheDocument()
    expect(screen.getAllByTestId('attachment-item')).toHaveLength(2)
    expect(screen.getByText('test-file.pdf')).toBeInTheDocument()
    expect(screen.getByText('image.png')).toBeInTheDocument()
  })

  it('does not render attachments when message has no attachments', () => {
    render(<ChatBubble message={mockMessages[0]} />)
    expect(screen.queryByTestId('attachment-bubble')).not.toBeInTheDocument()
  })
})
