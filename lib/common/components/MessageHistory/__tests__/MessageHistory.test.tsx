import { render, screen, within } from '@testing-library/react'
import { AppVariant, Chat } from '../../../types'
import { MessageHistory } from '../MessageHistory'
import { mockMessages } from '../../../mocks/messages'
import { mockChatList } from '../../../mocks/chatlist'
import { expect, vi } from 'vitest'

vi.mock('@common/components/MessageHistory/MessageHistoryHeader', () => ({
  MessageHistoryHeader: vi.fn(() => (
    <div data-testid="message-history-header" />
  )),
}))

let appVariantMock = AppVariant.Buyer

vi.mock('@common/contexts/MessageCenterContext', async () => {
  const actual = await import('@common/contexts/MessageCenterContext')
  return {
    MessageCenterContext: actual.MessageCenterContext,
    useMessageCenterContext: () => ({
      settings: { appVariant: appVariantMock },
    }),
  }
})

describe('MessageHistory Component', () => {
  const chat: Chat = mockChatList[0]
  const scrollIntoViewMock = vi.fn()
  beforeAll(() => {
    Element.prototype.scrollIntoView = vi.fn()
    HTMLElement.prototype.scrollIntoView = scrollIntoViewMock
  })

  it('renders MessageHistoryHeader and messages', () => {
    render(<MessageHistory messages={mockMessages} chat={chat} />)
    expect(screen.getByTestId('message-history-header')).toBeInTheDocument()
    mockMessages.forEach((msg) => {
      const expectedText = msg.content.replace(/\s+/g, ' ').trim()
      const messageEl = screen.getByTestId(`message-${msg.id}`)
      expect(messageEl).toHaveTextContent(expectedText)
    })
  })
  it('applies correct padding class based on appVariant', () => {
    appVariantMock = AppVariant.Employee

    const { container } = render(
      <MessageHistory messages={mockMessages} chat={chat} />
    )
    const wrapper = container.firstChild as HTMLElement
    expect(wrapper.className.includes('mc-pb-10')).toBe(true)
  })

  //commenting as the feature is removed for time being.
  // it('scrolls to the last message when messages change', () => {
  //   const { rerender } = render(
  //     <MessageHistory messages={mockMessages.slice(0, 1)} chat={chat} />
  //   )
  //
  //   rerender(<MessageHistory messages={mockMessages} chat={chat} />)
  //
  //   expect(scrollIntoViewMock).toHaveBeenCalledWith({
  //     behavior: 'smooth',
  //     block: 'end',
  //     inline: 'nearest',
  //   })
  // })

  it('renders children inside header wrapper', () => {
    render(
      <MessageHistory messages={mockMessages} chat={chat}>
        <div data-testid="child-element">Test Child</div>
      </MessageHistory>
    )
    expect(screen.getByTestId('child-element')).toBeInTheDocument()
  })
})
