import { render, screen } from '@testing-library/react'
import { MessageHistoryHeader } from '../MessageHistoryHeader'
import { mockChatList } from '../../../mocks/chatlist'
import { describe, expect } from 'vitest'

describe('message history header', async () => {
  it('renders the MessageHistoryHeader component', () => {
    const { container } = render(
      <MessageHistoryHeader chat={mockChatList[0]} />
    )
    expect(container).toMatchSnapshot()
  })

  it('renders the elements as expected', () => {
    const mockChat = mockChatList[0]
    render(<MessageHistoryHeader chat={mockChat} />)
    expect(screen.getByText('John Doe')).toBeInTheDocument()
    expect(screen.getByText(mockChat.order.orderNumber)).toBeInTheDocument()
    expect(screen.getByText('22.05.2025')).toBeInTheDocument()
  })
})
