// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`message history header > renders the MessageHistoryHeader component 1`] = `
<div>
  <header
    class="mc-mb-20 mc-font-bold"
    role="presentation"
  >
    <span
      class="mc-sr-only"
    >
      MESSAGE_CENTER.CHAT.DETAILS
    </span>
    <div
      class="mc-flex mc-justify-between mc-mb-20"
    >
      <div
        class="mc-flex mc-items-center mc-gap-2"
      >
        <div
          aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY"
          class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-orange-tint-80 mc-border-[1px] mc-border-metro-orange"
        >
          MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY
        </div>
      </div>
      <time
        aria-label="MESSAGE_CENTER.CHAT_CREATED_ON 22.05.2025"
        datetime="2025-05-22T17:37:18.000Z"
      >
        22.05.2025
      </time>
    </div>
    <div
      class="mc-text-metro-blue-main"
    >
      <p
        class="mc-text-2lg mc-mb-16"
      >
        John
         
        Doe
      </p>
      <p
        class="mc-text-lg"
      >
        O25-476446721693
      </p>
      <p
        aria-label="MESSAGE_CENTER.SUBJECT: MESSAGE_CENTER.BUYER.SUBJECT.ORDERS > MESSAGE_CENTER.BUYER.SUBJECT.ORDER_TRACKING"
        class="mc-text-lg mc-mt-8"
      >
        MESSAGE_CENTER.BUYER.SUBJECT.ORDERS &gt; MESSAGE_CENTER.BUYER.SUBJECT.ORDER_TRACKING
      </p>
    </div>
  </header>
</div>
`;
