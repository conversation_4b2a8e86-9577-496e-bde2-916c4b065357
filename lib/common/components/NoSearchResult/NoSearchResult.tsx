import { Props } from '@common/components/NoSearchResult/types'
import { useTranslation } from 'react-i18next'

export const NoSearchResult = ({ searchText }: Props) => {
  const { t } = useTranslation()
  return (
    <div
      className="mc-h-125 mc-flex mc-flex-col mc-place-content-between"
      role="alert"
      aria-live="polite"
    >
      <p className="mc-text-tiny mc-text-grey-tint-20">
        {t('MESSAGE_CENTER.CHAT.SEARCH.INPUT.EXAMPLE')}
      </p>
      <div className="mc-flex mc-flex-col mc-text-center">
        <p className="mc-font-bold">
          {t('MESSAGE_CENTER.CHAT.SEARCH.NO.RESULT.HEADING')} &quot;{searchText}
          &quot;
        </p>
        <p>{t('MESSAGE_CENTER.CHAT.SEARCH.NO.RESULT.BODY')}</p>
      </div>
    </div>
  )
}
