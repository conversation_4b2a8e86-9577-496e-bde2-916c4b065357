import { render, screen } from '@testing-library/react'
import { OrderDetails } from './OrderDetails'
import { Order } from '../../types'
import { vi } from 'vitest'

const mockOrder: Order = {
  orderNumber: '12345',
  placedAt: '2025-02-21T14:36:30+00:00',
  orderLines: [
    {
      name: 'Product 1',
      image: '',
      gtin: '8806090961878',
      sku: 'sku1',
      mid: 'AAA0000393410',
      quantity: 2,
      status: 'PLACED',
      id: '123',
    },
    {
      name: 'Product 2',
      image: '',
      gtin: '8806090961878',
      sku: 'sku2',
      mid: 'AAA0000393410',
      quantity: 1,
      status: 'PLACED',
      id: '345',
    },
  ],
}

vi.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
  }),
}))

describe('OrderDetails', () => {
  it('renders order details correctly', () => {
    render(<OrderDetails order={mockOrder} />)

    expect(
      screen.getByText('MESSAGE_CENTER.ORDER.CONTAINER_TITLE')
    ).toBeInTheDocument()
    expect(screen.getByText('12345')).toBeInTheDocument()
    expect(
      screen.getByText(/MESSAGE_CENTER.ORDER.PLACED_AT 21.02.2025/)
    ).toBeInTheDocument()
    expect(screen.getByText('Product 1')).toBeInTheDocument()
    expect(screen.getByText('Product 2')).toBeInTheDocument()
  })
})
