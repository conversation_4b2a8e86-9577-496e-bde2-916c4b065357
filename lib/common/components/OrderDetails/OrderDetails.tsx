import { FC } from 'react'
import { OrderItem } from './OrderItem'
import { AppVariant, Order } from '@common/types'
import { useTranslation } from 'react-i18next'
import { parseDate } from '@common/utils/date.util'
import { CollapsiblePanel } from '@common/components/CollapsiblePanel/CollapsiblePanel'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import { Card } from '@common/components'

export interface OrderDetailsProps {
  order?: Order
  isExpanded?: boolean
}

export const OrderDetails: FC<OrderDetailsProps> = ({
  order,
  isExpanded = false,
}) => {
  const { t } = useTranslation()
  const { settings } = useMessageCenterContext()

  if (!order) {
    return null
  }

  const WrapperComponent =
    settings.appVariant === AppVariant.Buyer ? Card : CollapsiblePanel

  const wrapperProps =
    settings.appVariant === AppVariant.Buyer
      ? { title: t('MESSAGE_CENTER.ORDER.CONTAINER_TITLE') }
      : {
          isExpanded,
          title: t('MESSAGE_CENTER.ORDER.CONTAINER_TITLE'),
        }

  return (
    <WrapperComponent {...wrapperProps}>
      <section aria-label={t('MESSAGE_CENTER.ORDER.CONTAINER_TITLE')}>
        <p
          className="mc-flex mc-items-center mc-gap-2 mc-text-lg mc-font-extrabold mc-text-metro-blue-main"
          aria-label={`${t('MESSAGE_CENTER.ORDER_NUMBER')}: ${order.orderNumber}}`}
        >
          {order.orderNumber}
        </p>
        <time
          className="mc-py-5 mc-text-metro-blue-main"
          dateTime={new Date(order.placedAt).toISOString()}
          aria-label={`${t('MESSAGE_CENTER.ORDER.PLACED_AT')} ${parseDate(order.placedAt)}`}
        >
          {t('MESSAGE_CENTER.ORDER.PLACED_AT')} {parseDate(order.placedAt)}
        </time>
        <ul className="mc-py-16 mc-w-full">
          {order.orderLines.map((item) => (
            <li key={item.id}>
              <OrderItem item={item} />
            </li>
          ))}
        </ul>
      </section>
    </WrapperComponent>
  )
}
