import { render, screen } from '@testing-library/react'
import { OrderItem } from './OrderItem'

import { AppVariant, OrderItemType, OrderLineStatus } from '@common/types'
import { vi } from 'vitest'

let appVariantMock = AppVariant.Buyer

vi.mock('@common/contexts/MessageCenterContext', async () => {
  const actual = await import('@common/contexts/MessageCenterContext')
  return {
    MessageCenterContext: actual.MessageCenterContext,
    useMessageCenterContext: () => ({
      settings: { appVariant: appVariantMock },
    }),
  }
})

vi.mock('@common/components/Badge/OrderLineStatusBadge', () => ({
  OrderLineStatusBadge: ({ status }: { status: string }) => (
    <div data-testid="status-badge">{status}</div>
  ),
}))

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

const mockItem: OrderItemType = {
  id: 'item-1',
  name: 'Test Product',
  image: 'https://example.com/image.jpg',
  gtin: '1234567890123',
  sku: 'SKU123',
  mid: 'MID456',
  quantity: 2,
  status: OrderLineStatus.SHIPPED,
}

describe('OrderItem component', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })

  it('renders basic fields for Buyer variant', () => {
    appVariantMock = AppVariant.Buyer

    render(<OrderItem item={mockItem} />)

    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(
      screen.getByText('MESSAGE_CENTER.ORDER_LINE.QUANTITY: 2')
    ).toBeInTheDocument()
    expect(screen.queryByText(/GTIN:/)).not.toBeInTheDocument()
    expect(screen.queryByTestId('status-badge')).not.toBeInTheDocument()
  })

  it('renders all fields for non-Buyer variant', () => {
    appVariantMock = AppVariant.Seller

    render(<OrderItem item={mockItem} />)

    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(
      screen.getByText('MESSAGE_CENTER.ORDER_LINE.QUANTITY: 2')
    ).toBeInTheDocument()
    expect(screen.getByText('GTIN: 1234567890123')).toBeInTheDocument()
    expect(screen.getByText('SKU: SKU123')).toBeInTheDocument()
    expect(screen.getByText('MID: MID456')).toBeInTheDocument()
    expect(screen.getByTestId('status-badge')).toHaveTextContent('SHIPPED')
  })

  it('handles null SKU', () => {
    appVariantMock = AppVariant.Seller

    const itemWithNullSKU = { ...mockItem, sku: null }

    render(<OrderItem item={itemWithNullSKU} />)

    expect(screen.getByText('SKU:')).toBeInTheDocument()
  })

  it('renders the item image with correct alt text and src', () => {
    appVariantMock = AppVariant.Buyer

    render(<OrderItem item={mockItem} />)

    const image = screen.getByAltText('Test Product') as HTMLImageElement
    expect(image).toBeInTheDocument()
    expect(image.src).toBe(mockItem.image)
  })
})
