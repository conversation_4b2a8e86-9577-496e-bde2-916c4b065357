import { AppVariant, OrderItemType } from '@common/types'
import { OrderLineStatusBadge } from '@common/components/Badge/OrderLineStatusBadge'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import { useTranslation } from 'react-i18next'

export function OrderItem({ item }: { item: OrderItemType }) {
  const {
    settings: { appVariant },
  } = useMessageCenterContext()
  const { t } = useTranslation()

  return (
    <article
      className="mc-flex mc-gap-2 mc-items-center mc-my-2 mc-p-16 mc-border-b mc-border-b-grey-tint-80 last:mc-border-b-0"
      aria-label={`${t('MESSAGE_CENTER.ORDER_LINE.PROD_NAME')}: ${item.name}. ${t('MESSAGE_CENTER.ORDER_LINE.QUANTITY')}: ${item.quantity}`}
    >
      <div className="mc-flex mc-flex-1 mc-items-start mc-justify-between mc-gap-16">
        <figure className="mc-flex mc-items-start mc-gap-16 mc-flex-grow">
          <div className="mc-bg-white-main mc-w-[3rem] mc-h-[3rem] mc-flex-shrink-0 mc-border mc-border-[#E6E8EB] mc-rounded-[2px]">
            <img
              src={item.image}
              alt={item.name}
              aria-hidden="true"
              className="mc-w-full mc-h-full mc-object-contain"
            />
          </div>
          <figcaption className="mc-text-grey-tint-40 mc-flex-grow mc-max-w-80 mc-overflow-hidden">
            <p className="mc-text-metro-blue-main">{item.name}</p>
            {appVariant !== AppVariant.Buyer && (
              <div className="mc-flex-shrink-0 mc-ml-auto">
                <OrderLineStatusBadge status={item.status} />
              </div>
            )}
            <p className="mc-py-8">
              {t('MESSAGE_CENTER.ORDER_LINE.QUANTITY')}: {item.quantity}
            </p>
            {appVariant !== AppVariant.Buyer && (
              <>
                <p className="mc-pt-10">GTIN: {item.gtin}</p>
                <p className="mc-truncate">SKU: {item.sku}</p>
                <p>MID: {item.mid}</p>
              </>
            )}
          </figcaption>
        </figure>
      </div>
    </article>
  )
}
