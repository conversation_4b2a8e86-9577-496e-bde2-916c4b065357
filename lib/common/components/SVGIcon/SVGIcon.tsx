import { FC } from 'react'
import { SVGIconProps } from './types'
import { ICONS, PathProps } from '../../icons'

export const SVGIcon: FC<SVGIconProps> = ({
  name,
  fill = 'currentColor',
  svgFill = '',
  stroke = 'none',
  viewBox = null,
  height = '100%',
  width = '1px',
  className = 'mc-block mc-align-middle',
  dataTestid = '',
  ...rest
}) => {
  const icon: { path: (props: PathProps) => JSX.Element; viewBox?: string } =
    ICONS[name]

  return (
    <svg
      {...rest}
      data-testid={dataTestid}
      width={width}
      height={height}
      className={className}
      fill={svgFill}
      xmlns="http://www.w3.org/2000/svg"
      viewBox={viewBox || icon?.viewBox}
      xmlnsXlink="http://www.w3.org/1999/xlink"
      aria-hidden="true"
    >
      {icon?.path({ fill, stroke })}
    </svg>
  )
}
