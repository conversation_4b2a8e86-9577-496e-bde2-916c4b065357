import { SVGIcon } from '@common/components/SVGIcon/SVGIcon'
import { SalesChannel } from '@common/types'
import { px2rem } from '@common/utils/px2rem'

interface Props {
  salesChannel: SalesChannel
}

export const SalesChannelBadge = ({ salesChannel }: Props): JSX.Element => {
  if (!salesChannel) {
    return <></>
  }

  return (
    <div className="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main">
      <SVGIcon
        dataTestid="svg"
        name={salesChannel.toLowerCase()}
        width={px2rem(18)}
        height={px2rem(18)}
      />
      <span className="mc-text-base mc-ml-4">{salesChannel.toUpperCase()}</span>
    </div>
  )
}
