import { FC, useEffect, useState } from 'react'
import { ONLY_ALPHANUMERIC_NO_SPACES_WITH_HYPHEN } from '@common/constants/regular-expressions.constants'
import SearchInput from '@common/components/SearchInput/SearchInput'
import { useTranslation } from 'react-i18next'
import { CHAT_SEARCH_TIMEOUT_IN_MILLISECONDS } from '@common/constants'

interface Props {
  onChange: (searchText: string) => void
  value?: string
}

export const SearchChat: FC<Props> = ({ onChange, value }) => {
  const [searchText, setSearchText] = useState(value || '')
  const isInputValid =
    searchText === '' ||
    ONLY_ALPHANUMERIC_NO_SPACES_WITH_HYPHEN.test(searchText)
  const { t } = useTranslation()

  useEffect(() => {
    if (value !== undefined) {
      setSearchText(value)
    }
  }, [value])

  useEffect(() => {
    const debounce = setTimeout(() => {
      if (isInputValid) {
        onChange(searchText)
      }
    }, CHAT_SEARCH_TIMEOUT_IN_MILLISECONDS)
    return () => {
      clearTimeout(debounce)
    }
  }, [searchText])

  const onChangeInput = (inputText: string) => {
    const trimmedInput = inputText.trim()
    setSearchText(trimmedInput)
  }

  return (
    <>
      <SearchInput
        placeholder={t('MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER')}
        onChange={onChangeInput}
        value={searchText}
        invalid={!isInputValid}
        id="searh-chat"
      />
      {!isInputValid && (
        <div className="mc-text-red-main mc-text-tiny">
          {t('MESSAGE_CENTER.CHAT.SEARCH.INVALID.SPECIAL.CHARACTERS')}
        </div>
      )}
    </>
  )
}
