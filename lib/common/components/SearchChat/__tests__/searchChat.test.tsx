import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { SearchChat } from '../SearchChat'
import { CHAT_SEARCH_TIMEOUT_IN_MILLISECONDS } from '@common/constants'

describe('SearchChat', () => {
  it('renders the SearchInput component', () => {
    render(<SearchChat onChange={vi.fn()} />)
    expect(
      screen.getByPlaceholderText(
        'MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER'
      )
    ).toBeInTheDocument()
  })

  it('calls onChange with valid input after debounce', async () => {
    const onChange = vi.fn()
    render(<SearchChat onChange={onChange} />)

    const input = screen.getByPlaceholderText(
      'MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER'
    )
    fireEvent.change(input, { target: { value: 'valid-input' } })

    await waitFor(
      () => {
        expect(onChange).toHaveBeenCalledWith('valid-input')
      },
      { timeout: CHAT_SEARCH_TIMEOUT_IN_MILLISECONDS + 500 }
    )
  })

  it('does not call onChange with invalid input', () => {
    const onChange = vi.fn()
    render(<SearchChat onChange={onChange} />)

    const input = screen.getByPlaceholderText(
      'MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER'
    )
    fireEvent.change(input, { target: { value: 'invalid input!' } })

    setTimeout(() => {
      expect(onChange).not.toHaveBeenCalled()
    }, CHAT_SEARCH_TIMEOUT_IN_MILLISECONDS)
  })

  it('shows error message for invalid input', async () => {
    render(<SearchChat onChange={vi.fn()} />)

    const input = screen.getByPlaceholderText(
      'MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER'
    )
    fireEvent.change(input, { target: { value: 'invalid input!' } })

    await waitFor(() => {
      expect(
        screen.getByText(
          'MESSAGE_CENTER.CHAT.SEARCH.INVALID.SPECIAL.CHARACTERS'
        )
      ).toBeInTheDocument()
    })
  })
})
