import { useState, useEffect } from 'react'
import Input from '@common/components/Input/Input'
import { SVGIcon } from '@common/components/SVGIcon/SVGIcon'
import { SVG_NAMES } from '@common/icons'
import classNames from 'classnames'
import { px2rem } from '@common/utils/px2rem'
import { useTranslation } from 'react-i18next'

interface Props {
  delay?: number
  onChange: (value: any) => void
  placeholder?: string
  searchDisabled?: boolean
  className?: string
  value?: string
  invalid?: boolean
  id?: string
  ariaLabel?: string
  ariaRole?: string
}

const SearchInput = ({
  delay = 300,
  onChange,
  searchDisabled = false,
  className,
  value,
  placeholder,
  invalid = false,
  id,
  ariaLabel,
  ariaRole,
}: Props) => {
  const [searchTerm, setSearchTerm] = useState(value || '')
  const searchClassName = classNames('mc-pl-32', className)
  const { t } = useTranslation()

  useEffect(() => {
    const handler = setTimeout(() => {
      onChange(searchTerm)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [searchTerm, delay])

  useEffect(() => {
    setSearchTerm(value ?? '')
  }, [value])

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value)
  }

  return (
    <div
      className="mc-relative mc-overflow-hidden"
      aria-label={ariaRole || t('MESSAGE_CENTER.CHATS.FILTERS.SEARCH.TEXT')}
      role="search"
    >
      <SVGIcon
        dataTestid="search-icon"
        name={SVG_NAMES.SEARCH}
        width={px2rem(20)}
        height={px2rem(20)}
        className="mc-block mc-align-middle mc-absolute mc-top-10 mc-left-10"
      />
      <Input
        className={searchClassName}
        type="text"
        value={searchTerm}
        onChange={handleChange}
        disabled={searchDisabled}
        placeholder={placeholder}
        invalid={invalid}
        id={id}
        ariaLabel={ariaLabel || placeholder}
      />
      {!!searchTerm.trim().length && (
        <button
          aria-label={t('MESSAGE_CENTER.CLEAR_SEARCH_INPUT')}
          onClick={() => setSearchTerm('')}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              setSearchTerm('')
            }
          }}
          className="mc-w-[18px] mc-h-[18px] mc-block mc-align-middle mc-absolute mc-border-none mc-mr-[8px]
          mc-cursor-pointer mc-text-grey-tint-20 mc-bg-grey-tint-90
           mc-right-[0px] mc-top-10 mc-rounded-10 mc-p-2"
        >
          <SVGIcon
            name={SVG_NAMES.CLEAR}
            dataTestid="clear-icon"
            width="15px"
            height="15px"
          />
        </button>
      )}
    </div>
  )
}
export default SearchInput
