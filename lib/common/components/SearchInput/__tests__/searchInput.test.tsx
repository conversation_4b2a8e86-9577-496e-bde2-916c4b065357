import { render, screen, fireEvent } from '@testing-library/react'
import SearchInput from '../SearchInput'
import '@testing-library/jest-dom'
describe('SearchInput', () => {
  it('calls onChange with the correct value after delay', () => {
    vi.useFakeTimers()
    const onChangeMock = vi.fn()
    render(<SearchInput onChange={onChangeMock} delay={300} />)

    fireEvent.change(screen.getByTestId('mc-input'), {
      target: { value: 'test' },
    })
    vi.advanceTimersByTime(300)

    expect(onChangeMock).toHaveBeenCalledWith('test')
  })

  it('clears the input when clear icon is clicked', () => {
    render(<SearchInput onChange={vi.fn()} value="test" />)

    fireEvent.click(screen.getByTestId('clear-icon'))
    expect(screen.getByTestId('mc-input')).toHaveValue('')
  })

  it('disables the input when searchDisabled is true', () => {
    render(<SearchInput onChange={vi.fn()} searchDisabled={true} />)
    expect(screen.getByTestId('mc-input')).toBeDisabled()
  })

  it('applies custom class to the input', () => {
    const className = 'custom-class'
    render(<SearchInput onChange={vi.fn()} className={className} />)
    expect(screen.getByTestId('mc-input')).toHaveClass(className)
  })
})
