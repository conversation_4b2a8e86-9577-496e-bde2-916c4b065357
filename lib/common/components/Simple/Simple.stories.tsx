import type { Meta, StoryObj } from '@storybook/react-vite'

// Simple test component
const SimpleComponent = ({ text }: { text: string }) => {
  return (
    <div
      style={{ padding: '16px', backgroundColor: '#0059e4', color: 'white' }}
    >
      {text}
    </div>
  )
}

const meta: Meta<typeof SimpleComponent> = {
  title: 'Test/Simple',
  component: SimpleComponent,
  parameters: {
    layout: 'centered',
  },
}

export default meta
type Story = StoryObj<typeof SimpleComponent>

export const Default: Story = {
  args: {
    text: 'Hello Storybook!',
  },
}
