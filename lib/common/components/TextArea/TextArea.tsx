import classNames from 'classnames'
import { FC, useEffect, useRef, useState } from 'react'
import { px2rem } from '@common/utils/px2rem'
import { DISABLED_BG_DIAGONAL_LINES_CLASS } from '@common/constants'

type Props = React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
  className?: string
  invalid?: boolean
  autoGrow?: boolean
  onChange: (event: React.ChangeEvent<HTMLTextAreaElement>) => void
}

const TextArea: FC<Props> = ({
  disabled,
  autoGrow,
  className,
  invalid,
  onChange,
  value,
  ...rest
}) => {
  const initialHeight = px2rem(40)
  const maxHeight = 150
  const textAreaRef = useRef<HTMLTextAreaElement>(null)
  const [height, setHeight] = useState(initialHeight)

  useEffect(() => {
    if (value === '') {
      setHeight(initialHeight)
    } else if (textAreaRef.current && autoGrow) {
      const newHeight = Math.min(textAreaRef.current.scrollHeight, maxHeight)
      setHeight(px2rem(newHeight))
    }
  }, [value])

  const textareaClassName = classNames(
    'mc-outline-none placeholder:mc-italic mc-placeholder-grey mc-w-full mc-border-[1px] ',
    'mc-border-grey-tint-80 mc-py-8 mc-px-12 mc-rounded-6 mc-h-40 mc-min-h-40 mc-max-h-[150px] ',
    'mc-transition-all mc-duration-300 mc-bg-white-main',
    className,
    {
      'mc-border-[1px] mc-border-metro-red': invalid,
      [DISABLED_BG_DIAGONAL_LINES_CLASS]: disabled,
    }
  )

  return (
    <textarea
      ref={textAreaRef}
      {...rest}
      disabled={disabled}
      data-testid="mc-textarea"
      className={textareaClassName}
      value={value}
      onChange={onChange}
      style={{
        height: height,
      }}
    />
  )
}

export default TextArea
