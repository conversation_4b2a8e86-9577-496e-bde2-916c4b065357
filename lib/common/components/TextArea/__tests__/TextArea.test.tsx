import { render, screen, fireEvent, act } from '@testing-library/react'
import { describe, it, vi, expect } from 'vitest'
import TextArea from '../TextArea'
import { px2rem } from '../../../utils/px2rem'

describe('TextArea Component', () => {
  it('renders correctly', () => {
    const mockOnChange = vi.fn()
    const { container } = render(<TextArea onChange={mockOnChange} />)
    expect(container).toMatchSnapshot()
  })

  it('calls onChange handler when input changes', () => {
    const handleChange = vi.fn()
    render(<TextArea onChange={handleChange} />)

    const textarea = screen.getByTestId('mc-textarea')
    fireEvent.change(textarea, { target: { value: 'Test input' } })

    expect(handleChange).toHaveBeenCalledTimes(1)
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({ value: 'Test input' }),
      })
    )
  })

  it('disables input when disabled prop is passed', () => {
    render(<TextArea onChange={vi.fn()} disabled />)
    const textarea = screen.getByTestId('mc-textarea')
    expect(textarea).toBeDisabled()
  })

  it('applies invalid class when invalid prop is passed', () => {
    render(<TextArea onChange={vi.fn()} invalid />)
    const textarea = screen.getByTestId('mc-textarea')
    expect(textarea).toHaveClass('mc-border-metro-red')
  })

  it('should increase height of the textarea when content grows but not exceed max height of px2rem(150)', () => {
    const { getByTestId, rerender } = render(
      <TextArea autoGrow value="" onChange={() => {}} />
    )
    const textarea = getByTestId('mc-textarea') as HTMLTextAreaElement
    vi.spyOn(window, 'getComputedStyle').mockImplementation((el) => {
      if (el === textarea) {
        return { height: px2rem(40) } as CSSStyleDeclaration
      }
      return {} as CSSStyleDeclaration
    })
    expect(window.getComputedStyle(textarea).height).toBe(px2rem(40))
    const longText = 'A \n'.repeat(500)
    fireEvent.input(textarea, { target: { value: longText } })
    vi.spyOn(window, 'getComputedStyle').mockImplementation((el) => {
      if (el === textarea) {
        return { height: px2rem(120) } as CSSStyleDeclaration
      }
      return {} as CSSStyleDeclaration
    })
    rerender(<TextArea autoGrow value={longText} onChange={() => {}} />)
    const newHeight = parseInt(window.getComputedStyle(textarea).height, 10)
    expect(newHeight).toBeGreaterThan(parseInt(px2rem(40), 10))
    expect(newHeight).toBeLessThanOrEqual(parseInt(px2rem(150), 10))
  })
})
