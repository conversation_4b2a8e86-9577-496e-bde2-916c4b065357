import { FC, ReactNode, useState } from 'react'

interface TooltipProps {
  children: ReactNode
  content: ReactNode
  position?: 'top' | 'bottom' | 'left' | 'right'
  className?: string
}

export const Tooltip: FC<TooltipProps> = ({
  children,
  content,
  position = 'bottom',
  className = '',
}) => {
  const [showTooltip, setShowTooltip] = useState(false)

  const positionClasses = {
    top: 'mc-bottom-full mc-mb-5',
    bottom: 'mc-top-full mc-mt-5',
    left: 'mc-right-full mc-mr-5',
    right: 'mc-left-full mc-ml-5',
  }

  const arrowPositionClasses = {
    top: 'mc--bottom-2 mc-left-1/2 mc--translate-x-1/2',
    bottom: 'mc--top-2 mc-left-1/2 mc--translate-x-1/2',
    left: 'mc--right-2 mc-top-1/2 mc--translate-y-1/2',
    right: 'mc--left-2 mc-top-1/2 mc--translate-y-1/2',
  }

  return (
    <div
      className={`mc-relative mc-cursor-pointer ${className}`}
      onMouseEnter={() => setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
      tabIndex={0}
      onFocus={() => setShowTooltip(true)}
      onBlur={() => setShowTooltip(false)}
      aria-describedby="tooltip"
    >
      {children}
      {showTooltip && (
        <div
          role="tooltip"
          id="tooltip"
          className={`mc-absolute mc-left-1/2 mc-transform mc--translate-x-1/2 mc-w-64 mc-bg-black mc-text-white-main mc-text-xs mc-rounded-md mc-p-2 mc-z-10 ${positionClasses[position]}`}
        >
          <div
            className={`mc-absolute mc-w-6 mc-h-6 mc-bg-black mc-transform mc-rotate-45 ${arrowPositionClasses[position]}`}
          ></div>
          {content}
        </div>
      )}
    </div>
  )
}
