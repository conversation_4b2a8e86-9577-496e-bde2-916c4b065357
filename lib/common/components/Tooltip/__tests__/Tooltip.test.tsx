import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { Tooltip } from '../Tooltip'

describe('Tooltip Component', () => {
  it('renders correctly and matches snapshot', () => {
    const { container } = render(
      <Tooltip content="Tooltip content">
        <button>Hover me</button>
      </Tooltip>
    )
    expect(container).toMatchSnapshot()
  })

  it('shows tooltip on mouse enter and hides on mouse leave', () => {
    render(
      <Tooltip content="Tooltip content">
        <button>Hover me</button>
      </Tooltip>
    )

    const trigger = screen.getByText('Hover me')

    // Initially tooltip should not be visible
    expect(screen.queryByText('Tooltip content')).not.toBeInTheDocument()

    // Show tooltip on mouse enter
    fireEvent.mouseEnter(trigger)
    expect(screen.getByText('Tooltip content')).toBeInTheDocument()

    // Hide tooltip on mouse leave
    fireEvent.mouseLeave(trigger)
    expect(screen.queryByText('Tooltip content')).not.toBeInTheDocument()
  })

  it('applies correct position classes based on position prop', () => {
    const { rerender } = render(
      <Tooltip content="Tooltip content" position="top">
        <button>Hover me</button>
      </Tooltip>
    )

    const trigger = screen.getByText('Hover me')
    fireEvent.mouseEnter(trigger)

    let tooltip = screen.getByText('Tooltip content')
    expect(tooltip).toHaveClass('mc-bottom-full')

    // Test bottom position (default)
    fireEvent.mouseLeave(trigger)
    rerender(
      <Tooltip content="Tooltip content">
        <button>Hover me</button>
      </Tooltip>
    )
    fireEvent.mouseEnter(trigger)
    tooltip = screen.getByText('Tooltip content')
    expect(tooltip).toHaveClass('mc-top-full')
  })

  it('applies custom className when provided', () => {
    render(
      <Tooltip content="Tooltip content" className="custom-class">
        <button>Hover me</button>
      </Tooltip>
    )

    const wrapper = screen.getByText('Hover me').parentElement
    expect(wrapper).toHaveClass('custom-class')
  })
})
