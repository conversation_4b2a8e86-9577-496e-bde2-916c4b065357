import { useRef } from 'react'
import Button from '@common/components/Button/Button'
import { SVG_NAMES } from '@common/icons'
import { useTranslation } from 'react-i18next'

interface UploadButtonProps {
  onFileSelect: (file: File | File[]) => void
  accept?: string
  children: React.ReactNode
  multiple?: boolean
  maxFiles?: number
  disabled?: boolean
}

export const UploadButton = ({
  onFileSelect,
  accept = '*',
  children,
  multiple = false,
  maxFiles = 1,
  disabled = false,
}: UploadButtonProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { t } = useTranslation()
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (!files || files.length === 0) return

    if (multiple && files.length > 1) {
      // Convert FileList to array and limit to maxFiles
      const fileArray = Array.from(files).slice(0, maxFiles)
      onFileSelect(fileArray)
    } else {
      onFileSelect(files[0])
    }

    // Reset the input so the same file can be selected again
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="mc-relative">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept={accept}
        multiple={multiple}
        className="mc-absolute mc-opacity-0 mc-w-full mc-h-full mc-z-1"
        style={{ top: 0, left: 0 }}
        data-testid="mc-upload-button"
        aria-label={t('MESSAGE_CENTER.CHAT.UPLOAD.ATTACH_FILES')}
        tabIndex={-1}
      />
      <Button
        onClick={() => fileInputRef.current?.click()}
        icon={disabled ? SVG_NAMES.UPLOAD_DISABLED : SVG_NAMES.UPLOAD}
        secondary
        disabled={disabled}
        className="mc-pl-[23px] md:!mc-pl-36"
      >
        {children}
      </Button>
    </div>
  )
}

export default UploadButton
