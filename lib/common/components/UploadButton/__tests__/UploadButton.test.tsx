import { render, screen, fireEvent } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { UploadButton } from '../UploadButton'

describe('UploadButton Component', () => {
  it('renders correctly and matches snapshot', () => {
    const onFileSelect = vi.fn()
    const { container } = render(
      <UploadButton onFileSelect={onFileSelect}>Upload File</UploadButton>
    )
    expect(container).toMatchSnapshot()
  })

  it('calls onFileSelect when a file is selected', () => {
    const onFileSelect = vi.fn()
    render(<UploadButton onFileSelect={onFileSelect}>Upload File</UploadButton>)

    const input = screen.getByRole('button').previousSibling as HTMLInputElement
    const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })

    Object.defineProperty(input, 'files', {
      value: [file],
    })

    fireEvent.change(input)

    expect(onFileSelect).toHaveBeenCalledWith(file)
  })

  it('accepts custom file types', () => {
    const onFileSelect = vi.fn()
    render(
      <UploadButton onFileSelect={onFileSelect} accept=".csv,.xlsx">
        Upload Spreadsheet
      </UploadButton>
    )

    const input = screen.getByRole('button').previousSibling as HTMLInputElement
    expect(input.accept).toBe('.csv,.xlsx')
  })

  it('renders children correctly', () => {
    const onFileSelect = vi.fn()
    render(
      <UploadButton onFileSelect={onFileSelect}>
        Custom Button Text
      </UploadButton>
    )

    expect(screen.getByText('Custom Button Text')).toBeInTheDocument()
  })
})
