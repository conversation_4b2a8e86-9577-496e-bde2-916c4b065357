import { renderHook, waitFor, act } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useFilesUpload } from '../useFilesUpload'

describe('useFilesUpload', () => {
  const mockFile = new File(['test content'], 'test.jpg', {
    type: 'image/jpeg',
  })
  const mockSignedUrl = 'https://example.com/upload-url'

  const mockGetSignedUrl = vi.fn(
    (files: { id: string; contentType: string }[]) => {
      return Promise.resolve({
        data: files.map((file) => ({
          id: file.id,
          fileId: `file-${file.id}`,
          signedUrl: mockSignedUrl,
        })),
        status: 'success',
      })
    }
  )

  beforeEach(() => {
    vi.clearAllMocks()
    global.fetch = vi.fn().mockResolvedValue({ ok: true })
  })

  it('initializes with empty files array', () => {
    const { result } = renderHook(() => useFilesUpload(mockGetSignedUrl))
    expect(result.current.files).toEqual([])
  })

  it('adds a file with id when addFile is called', async () => {
    const { result } = renderHook(() => useFilesUpload(mockGetSignedUrl))

    await act(async () => {
      await result.current.addFile(mockFile)
    })

    // Wait for state update to complete
    await waitFor(() => {
      expect(result.current.files.length).toBe(1)
    })

    expect(result.current.files[0]).toHaveProperty('id')
    expect(result.current.files[0].contentType).toBe('image/jpeg')
    expect(result.current.files[0].file).toBe(mockFile)
  })

  it('uploads files using fetch when uploadFiles is called', async () => {
    const { result } = renderHook(() => useFilesUpload(mockGetSignedUrl))

    await act(async () => {
      await result.current.addFile(mockFile)
    })

    // Wait for state update to complete
    await waitFor(() => {
      expect(result.current.files.length).toBe(1)
    })

    await act(async () => {
      await result.current.uploadFiles()
    })

    expect(global.fetch).toHaveBeenCalledWith(mockSignedUrl, {
      method: 'PUT',
      body: mockFile,
    })
  })

  it('adds multiple files correctly', async () => {
    const mockFile2 = new File(['another test'], 'test2.jpg', {
      type: 'image/jpeg',
    })

    const { result } = renderHook(() => useFilesUpload(mockGetSignedUrl))

    await act(async () => {
      await result.current.addFile(mockFile)
      await result.current.addFile(mockFile2)
    })

    await waitFor(() => {
      expect(result.current.files).toHaveLength(2)
    })
  })
})
