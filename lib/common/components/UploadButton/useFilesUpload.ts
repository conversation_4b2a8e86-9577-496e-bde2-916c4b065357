import { useState } from 'react'
import { SignedUrlResponse } from './types'
import { Attachment } from '@common/types'

export const useFilesUpload = (
  getSignedUrl: (
    files: { id: string; contentType: string }[]
  ) => Promise<SignedUrlResponse>
) => {
  const [files, setFiles] = useState<
    {
      file: File
      id: string
      contentType: string
      isLoading: boolean
    }[]
  >([])

  const addFile = async (file: File) => {
    const id = crypto.randomUUID()
    const contentType = file.type
    setFiles((files) => [...files, { file, id, contentType, isLoading: false }])
  }

  const removeFile = (id: string) => {
    setFiles((files) => files.filter((file) => file.id !== id))
  }

  const uploadFiles = async () => {
    const { data: signedUrls, status, message } = await getSignedUrl(files)
    let attachments: Attachment[] = []

    if (status !== 'success') {
      throw new Error(message)
    } else {
      for (const file of files) {
        const signedFile = signedUrls.find((url) => url.id === file.id)

        if (!signedFile) {
          throw new Error('No signed URL found for file')
        }
        // change isLoading to true
        setFiles((files) =>
          files.map((f) => (f.id === file.id ? { ...f, isLoading: true } : f))
        )

        await fetch(signedFile?.signedUrl, {
          method: 'PUT',
          body: file.file,
        })

        // change isLoading to false
        setFiles((files) =>
          files.map((f) =>
            f.id === file.id
              ? { ...f, isLoading: false, fileId: signedFile.fileId }
              : f
          )
        )
        attachments.push({
          fileId: signedFile.fileId,
          fileName: file.file.name,
          fileType: file.file.type,
        })
      }

      setFiles([])

      return attachments
    }
  }

  return { files, addFile, removeFile, uploadFiles, setFiles }
}
