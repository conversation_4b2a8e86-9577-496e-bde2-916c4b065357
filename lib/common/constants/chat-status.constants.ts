export enum ChatBadgeStatus {
  NEW = 'NEW', // Seller, Buyer - when opening a chat and there is no message sent yet
  NEEDS_REPLY = 'NEEDS_REPLY', // Seller, Employee - when buyer send a message and seller didn’t reply to it
  OVER_SLA = 'OVER_SLA', // Seller, Employee -  when buyer sent a message and seller didn’t reply to it & > 24h passed
  INACTIVE = 'INACTIVE', // Seller, Buyer, Employee -  when last message in the chat was sent more than 72h ago
}

export const CHAT_STATUS_TEXT: Record<ChatBadgeStatus, string> = {
  [ChatBadgeStatus.NEW]: 'MESSAGE_CENTER.CHAT_STATUS.NEW',
  [ChatBadgeStatus.NEEDS_REPLY]: 'MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY',
  [ChatBadgeStatus.OVER_SLA]: 'MESSAGE_CENTER.CHAT_STATUS.OVER_SLA',
  [ChatBadgeStatus.INACTIVE]: 'MESSAGE_CENTER.CHAT_STATUS.INACTIVE',
}
