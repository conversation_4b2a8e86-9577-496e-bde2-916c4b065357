import { OrderLineStatus } from '@common/types/order-status.type'

export const ORDER_LINE_STATUS_TEXT_BUYER: Record<OrderLineStatus, string> = {
  [OrderLineStatus.PLACED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.PLACED',
  [OrderLineStatus.CANCELED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.CANCELED',
  [OrderLineStatus.CONFIRMED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.CONFIRMED',
  [OrderLineStatus.SHIPPED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.SHIPPED',
  [OrderLineStatus.RETURN_REQUEST]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.RETURN_STARTED',
  [OrderLineStatus.RETURN_REQUESTED]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.RETURN_STARTED',
  [OrderLineStatus.RETURN_ACCEPTED]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.RETURN_ACCEPTED',
  [OrderLineStatus.RETURN_DECLINED]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.RETURN_DECLINED',
  [OrderLineStatus.PENDING_VERIFICATION]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.PENDING_VERIFICATION',
  [OrderLineStatus.BLOCKED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.BLOCKED',
  [OrderLineStatus.WAITING_FOR_PAYMENT]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.WAITING_FOR_PAYMENT',
  [OrderLineStatus.PAID]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.PAID',
}

export const ORDER_LINE_STATUS_TEXT_SELLER: Record<OrderLineStatus, string> = {
  [OrderLineStatus.PLACED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.PLACED',
  [OrderLineStatus.CANCELED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.CANCELED',
  [OrderLineStatus.CONFIRMED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.CONFIRMED',
  [OrderLineStatus.SHIPPED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.SHIPPED',
  [OrderLineStatus.RETURN_REQUEST]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.RETURN_REQUEST',
  [OrderLineStatus.RETURN_REQUESTED]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.RETURN_REQUESTED',
  [OrderLineStatus.RETURN_ACCEPTED]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.RETURN_ACCEPTED',
  [OrderLineStatus.RETURN_DECLINED]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.RETURN_DECLINED',
  [OrderLineStatus.PENDING_VERIFICATION]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.PENDING_VERIFICATION',
  [OrderLineStatus.BLOCKED]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.BLOCKED',
  [OrderLineStatus.WAITING_FOR_PAYMENT]:
    'MESSAGE_CENTER.ORDER_LINE_STATUS.WAITING_FOR_PAYMENT',
  [OrderLineStatus.PAID]: 'MESSAGE_CENTER.ORDER_LINE_STATUS.PAID',
}
