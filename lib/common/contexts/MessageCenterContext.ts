import { createContext, MutableRefObject, useContext } from 'react'
import { LanguageCodes } from '@common/constants'
import { Settings, Subject } from '@common/types'
import { AppVariant } from '@common/types'

export const defaultSettings: Settings = {
  language: LanguageCodes.en,
  appVariant: AppVariant.Seller,
  supportedFormats: ['pdf', 'png', 'jpg', 'jpeg', 'xls', 'doc', 'docx', 'txt'],
  maxFileSize: '10MB',
  maxFiles: 2,
}

interface MessageCenterContextType {
  settings: Settings
  subjects: Subject[]
  cacheRef: MutableRefObject<Record<string, boolean>>
}
export const MessageCenterContext = createContext<MessageCenterContextType>({
  settings: defaultSettings,
  subjects: [],
  cacheRef: { current: {} } as any,
})
export const useMessageCenterContext = () => {
  const { cacheRef, ...rest } = useContext(MessageCenterContext)
  return { ...rest, cacheRef }
}
