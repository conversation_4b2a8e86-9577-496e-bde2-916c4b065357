import { MessageCenterContext, defaultSettings } from './MessageCenterContext'
import { PropsWithChildren, useEffect, useMemo, useRef } from 'react'
import { Settings, Subject } from '../types'
import { i18n } from '../../i18n'
import { useTranslation } from 'react-i18next'

interface AppProviderProps {
  settings: Partial<Settings>
  subjects: Subject[]
}

export const MessageCenterProvider = ({
  settings,
  subjects,
  children,
}: PropsWithChildren<AppProviderProps>): JSX.Element => {
  const { t } = useTranslation()
  const mergedSettings: Settings = {
    ...defaultSettings,
    ...settings,
  }

  const cacheRef = useRef<Record<string, boolean>>({})

  useEffect(() => {
    const changeLanguage = async () => {
      if (
        mergedSettings.language &&
        i18n.language !== mergedSettings.language
      ) {
        try {
          await i18n.changeLanguage(mergedSettings.language)
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error('Failed to change language:', error)
        }
      }
    }
    changeLanguage()
  }, [mergedSettings.language])

  const processedSubjects = useMemo(() => {
    const getTranslatedSubjects = (subjects: Subject[]): Subject[] => {
      return subjects.map((subject) => {
        const translatedSubject = t(
          `MESSAGE_CENTER.${subject.userType?.toUpperCase()}.SUBJECT.${subject?.subject}`,
          { lng: settings.language || 'en' }
        )

        return {
          ...subject,
          subjectText: translatedSubject,
          children: getTranslatedSubjects(subject.children ?? []),
        }
      })
    }
    return getTranslatedSubjects(subjects)
  }, [subjects])

  return (
    <MessageCenterContext.Provider
      value={{
        settings: mergedSettings,
        subjects: processedSubjects,
        cacheRef,
      }}
    >
      {children}
    </MessageCenterContext.Provider>
  )
}
