import { renderHook } from '@testing-library/react'
import { useSubject } from '../useSubject'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import { useTranslation } from 'react-i18next'

vi.mock('@common/contexts/MessageCenterContext', async () => {
  const actual = await vi.importActual('@common/contexts/MessageCenterContext')
  return {
    ...actual,
    useMessageCenterContext: vi.fn(),
  }
})

vi.mock('react-i18next', () => ({
  useTranslation: vi.fn(),
}))

describe('useSubject', () => {
  const mockuseMessageCenterContext = useMessageCenterContext as jest.Mock
  const mockUseTranslation = useTranslation as jest.Mock

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('returns translated subject from structured subject string', () => {
    const t = vi.fn((key: string) => {
      const translations: Record<string, string> = {
        'MESSAGE_CENTER.BUYER.SUBJECT.INVOICES': 'Invoices',
        'MESSAGE_CENTER.BUYER.SUBJECT.INVOICES_NOT_UPLOADED': 'Not Uploaded',
      }
      return translations[key] || key
    })

    mockuseMessageCenterContext.mockReturnValue({
      subjects: [],
    })

    mockUseTranslation.mockReturnValue({ t })

    const { result } = renderHook(() => useSubject())

    const chat = {
      subject: 'BUYER.INVOICES.INVOICES_NOT_UPLOADED',
    }

    const subject = result.current.getSubject(chat)
    expect(subject).toBe('Invoices > Not Uploaded')
  })

  it('returns raw subject when format is invalid', () => {
    mockuseMessageCenterContext.mockReturnValue({
      subjects: [],
    })

    mockUseTranslation.mockReturnValue({
      t: vi.fn((key) => key),
    })

    const { result } = renderHook(() => useSubject())

    const chat = {
      subject: 'BAD_FORMAT',
    }

    const subject = result.current.getSubject(chat)
    expect(subject).toBe('BAD_FORMAT')
  })
})
