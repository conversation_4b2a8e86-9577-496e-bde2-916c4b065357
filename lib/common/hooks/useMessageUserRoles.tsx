import { Message, UserType } from '@common/types'
import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'

export const useMessageUserRoles = (message: Message) => {
  const { settings } = useMessageCenterContext()

  const isReceiver =
    message?.senderUserType?.toLowerCase() ===
    settings.appVariant?.toLowerCase()

  const isSeller =
    message.senderUserType?.toLowerCase() === UserType.SELLER.toLowerCase()

  return { isReceiver, isSeller }
}
