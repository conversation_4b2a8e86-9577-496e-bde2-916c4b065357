import { useMessageCenterContext } from '@common/contexts/MessageCenterContext'
import { Chat } from '@common/types'
import { useTranslation } from 'react-i18next'

export const useSubject = () => {
  const { subjects } = useMessageCenterContext()
  const { t } = useTranslation()

  const getSubject = (chat: Chat): string => {
    if (chat.customSubject) {
      const otherSub = (subjects || []).find((sub) => sub.subject === 'OTHERS')
      return `${otherSub?.subjectText || otherSub?.subject} > ${chat.customSubject}`
    }

    const [useType, parent, leafNode] = chat.subject.split('.')
    if (useType && parent && leafNode) {
      const upperUseType = useType.toUpperCase()
      const upperParent = parent.toUpperCase()
      const upperLeafNode = leafNode?.toUpperCase() ?? ''

      const parentSubject = t(
        `MESSAGE_CENTER.${upperUseType}.SUBJECT.${upperParent}`
      )
      const leafSubject = t(
        `MESSAGE_CENTER.${upperUseType}.SUBJECT.${upperLeafNode}`
      )

      return `${parentSubject} > ${leafSubject}`
    }
    return chat.subject
  }

  return { getSubject, translatedSubjects: subjects }
}
