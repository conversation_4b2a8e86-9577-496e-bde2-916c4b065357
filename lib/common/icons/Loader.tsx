import { PathProps } from '@common/icons/index'

export const Loader = {
  viewBox: '0 0 42 42',
  path(props: PathProps) {
    return (
      <circle
        {...props}
        cx="50%"
        cy="50%"
        r="20"
        strokeLinecap="round"
        strokeWidth={props.strokeWidth || '2px'}
        id="loader-circle"
        fill="none"
        stroke={props.stroke || 'currentColor'}
        strokeDasharray="80, 200"
        strokeDashoffset="0"
        style={{
          transformOrigin: 'center',
          animation:
            'mc-spin 2s linear infinite, mc-dash 1.5s ease-in-out infinite',
        }}
      ></circle>
    )
  },
}
