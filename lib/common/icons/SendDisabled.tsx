export const SendDisabled = {
  viewBox: '0 0 20 23', // Increased height by 3px
  path(props: any) {
    return (
      <>
        <path
          {...props}
          d="M18.7949 1.8789C18.8367 1.8814 18.8785 1.88528 18.919 1.89648C18.9463 1.90394 18.9706 1.91616 18.9961 1.92675C19.0205 1.93675 19.0472 1.94194 19.0703 1.95507C19.084 1.96314 19.0931 1.97504 19.1055 1.98437C19.1286 1.99937 19.149 2.01835 19.1709 2.0371C19.2027 2.06642 19.2319 2.09656 19.2569 2.13085C19.2662 2.14273 19.2787 2.15155 19.2862 2.16406C19.2935 2.17524 19.2946 2.18841 19.3008 2.20019C19.3214 2.2383 19.3365 2.27712 19.3496 2.31835C19.3577 2.34511 19.3658 2.37069 19.3701 2.39745C19.3764 2.43673 19.3753 2.47669 19.3741 2.51659C19.3728 2.54588 19.3741 2.57485 19.3692 2.60351C19.3673 2.61226 19.3691 2.6221 19.3672 2.63085L16.3233 16.3994C16.2814 16.5907 16.1509 16.7511 15.9727 16.833C15.8902 16.8705 15.8007 16.8896 15.7119 16.8896C15.6101 16.8896 15.5079 16.8648 15.416 16.8154L11.0205 14.4512L8.63772 18.4453C8.52268 18.6379 8.31632 18.75 8.10061 18.75C8.04574 18.75 7.98946 18.7431 7.9346 18.7275C7.66338 18.6531 7.47569 18.4068 7.47561 18.125V12.6025L1.02737 10.1504C0.786902 10.059 0.626902 9.82967 0.625027 9.57226C0.622647 9.31543 0.777525 9.08256 1.01565 8.98632L18.5225 1.91601C18.5393 1.90916 18.5568 1.9102 18.5742 1.90527C18.6205 1.89089 18.6671 1.8791 18.7158 1.87597C18.7427 1.87409 18.7681 1.87702 18.7949 1.8789ZM8.72659 15.8564L9.91897 13.8584L8.72659 13.2168V15.8564ZM9.17581 12.0391L15.2803 15.3223L17.7217 4.28027L9.17581 12.0391ZM2.96292 9.54882L7.96487 11.4502L15.7442 4.38671L2.96292 9.54882Z"
          fill="#001432"
        />
        <path
          {...props}
          d="M18.7949 1.8789C18.8367 1.8814 18.8785 1.88528 18.919 1.89648C18.9463 1.90394 18.9706 1.91616 18.9961 1.92675C19.0205 1.93675 19.0472 1.94194 19.0703 1.95507C19.084 1.96314 19.0931 1.97504 19.1055 1.98437C19.1286 1.99937 19.149 2.01835 19.1709 2.0371C19.2027 2.06642 19.2319 2.09656 19.2569 2.13085C19.2662 2.14273 19.2787 2.15155 19.2862 2.16406C19.2935 2.17524 19.2946 2.18841 19.3008 2.20019C19.3214 2.2383 19.3365 2.27712 19.3496 2.31835C19.3577 2.34511 19.3658 2.37069 19.3701 2.39745C19.3764 2.43673 19.3753 2.47669 19.3741 2.51659C19.3728 2.54588 19.3741 2.57485 19.3692 2.60351C19.3673 2.61226 19.3691 2.6221 19.3672 2.63085L16.3233 16.3994C16.2814 16.5907 16.1509 16.7511 15.9727 16.833C15.8902 16.8705 15.8007 16.8896 15.7119 16.8896C15.6101 16.8896 15.5079 16.8648 15.416 16.8154L11.0205 14.4512L8.63772 18.4453C8.52268 18.6379 8.31632 18.75 8.10061 18.75C8.04574 18.75 7.98946 18.7431 7.9346 18.7275C7.66338 18.6531 7.47569 18.4068 7.47561 18.125V12.6025L1.02737 10.1504C0.786902 10.059 0.626902 9.82967 0.625027 9.57226C0.622647 9.31543 0.777525 9.08256 1.01565 8.98632L18.5225 1.91601C18.5393 1.90916 18.5568 1.9102 18.5742 1.90527C18.6205 1.89089 18.6671 1.8791 18.7158 1.87597C18.7427 1.87409 18.7681 1.87702 18.7949 1.8789ZM8.72659 15.8564L9.91897 13.8584L8.72659 13.2168V15.8564ZM9.17581 12.0391L15.2803 15.3223L17.7217 4.28027L9.17581 12.0391ZM2.96292 9.54882L7.96487 11.4502L15.7442 4.38671L2.96292 9.54882Z"
          fill="white"
          fillOpacity="0.4"
        />
      </>
    )
  },
}
