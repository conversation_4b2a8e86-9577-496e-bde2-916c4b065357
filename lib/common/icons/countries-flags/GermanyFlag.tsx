import { PathProps } from '@common/icons'
import { px2rem } from '@common/utils/px2rem'

export const GermanyFlag = {
  viewBox: '0 0 24 24',
  path(props: PathProps) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        width={px2rem(24)}
        height={px2rem(24)}
        {...props}
      >
        <defs>
          <circle id="a" cx={12} cy={12} r={12} />
        </defs>
        <g fill="none" fillRule="evenodd">
          <mask id="b" fill="#fff">
            <use xlinkHref="#a" />
          </mask>
          <use xlinkHref="#a" fill="#D8D8D8" />
          <path
            fill="#FFDA43"
            fillRule="nonzero"
            d="M0 0h24v24H0z"
            mask="url(#b)"
          />
          <path
            fill="#D80027"
            fillRule="nonzero"
            d="M0 0h24v16H0z"
            mask="url(#b)"
          />
          <path
            fill="#000"
            fillRule="nonzero"
            d="M0 0h24v8H0z"
            mask="url(#b)"
          />
        </g>
      </svg>
    )
  },
}
