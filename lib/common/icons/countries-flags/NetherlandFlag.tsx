import { PathProps } from '@common/icons'
import { px2rem } from '@common/utils/px2rem'

export const NetherlandFlag = {
  viewBox: '0 0 24 24',
  path(props: PathProps) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        width={px2rem(24)}
        height={px2rem(24)}
        {...props}
      >
        <defs>
          <circle id="a" cx={12} cy={12} r={12} />
        </defs>
        <g fill="none" fillRule="evenodd">
          <mask id="b" fill="#fff">
            <use xlinkHref="#a" />
          </mask>
          <use xlinkHref="#a" fill="#D8D8D8" />
          <path
            fill="#0052B4"
            fillRule="nonzero"
            d="M0 0h24v24H0z"
            mask="url(#b)"
          />
          <path
            fill="#F0F0F0"
            fillRule="nonzero"
            d="M0 0h24v16H0z"
            mask="url(#b)"
          />
          <path
            fill="#A2001D"
            fillRule="nonzero"
            d="M0 0h24v8H0z"
            mask="url(#b)"
          />
        </g>
      </svg>
    )
  },
}
