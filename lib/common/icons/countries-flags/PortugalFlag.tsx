import { PathProps } from '@common/icons'
import { px2rem } from '@common/utils/px2rem'

export const PortugalFlag = {
  viewBox: '0 0 24 24',
  path(props: PathProps) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={px2rem(24)}
        height={px2rem(24)}
        fill="none"
        {...props}
      >
        <g clipPath="url(#a)">
          <mask
            id="b"
            width={24}
            height={24}
            x={0}
            y={0}
            maskUnits="userSpaceOnUse"
            style={{
              maskType: 'alpha',
            }}
          >
            <circle cx={12} cy={12} r={12} fill="#fff" />
          </mask>
          <g mask="url(#b)">
            <path fill="#6DA544" d="M0 24h7.828l1.777-12.202L7.828 0H0v24Z" />
            <path fill="#D80027" d="M24 0H7.828v24H24V0Z" />
            <path
              fill="#FFDA44"
              d="M7.828 16.172a4.172 4.172 0 1 0 0-8.344 4.172 4.172 0 0 0 0 8.344Z"
            />
            <path
              fill="#D80027"
              d="M5.48 9.914v2.602a2.346 2.346 0 0 0 4.692 0V9.909H5.484l-.004.005Z"
            />
            <path
              fill="#EEE"
              d="M7.828 13.303a.784.784 0 0 1-.783-.783v-1.036h1.566v1.032a.784.784 0 0 1-.783.782v.005Z"
            />
          </g>
        </g>
        <defs>
          <clipPath id="a">
            <path fill="#fff" d="M0 0h24v24H0z" />
          </clipPath>
        </defs>
      </svg>
    )
  },
}
