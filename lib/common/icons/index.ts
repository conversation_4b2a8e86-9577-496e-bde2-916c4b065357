import { FranceFlag } from './countries-flags/FranceFlag'
import { PortugalFlag } from './countries-flags/PortugalFlag'
import { NetherlandFlag } from './countries-flags/NetherlandFlag'
import { GermanyFlag } from './countries-flags/GermanyFlag'
import { SpainFlag } from './countries-flags/SpainFlag'
import { ItalyFlag } from './countries-flags/ItalyFlag'
import { SearchIcon } from '@common/icons/SearchIcon'
import { ClearIcon } from '@common/icons/ClearIcon'
import { SendIcon } from '@common/icons/SendIcon'
import { SubjectArrow } from '@common/icons/SubjectArrow'
import { InfoIconBlueBg } from './InfoIconBlueBg'
import { UploadIcon } from './UploadIcon'
import { Trash } from './Trash'
import { Loader } from './Loader'
import { DownloadIcon } from './DownloadIcon'
import { SendDisabled } from '@common/icons/SendDisabled'
import { LoaderDisabled } from '@common/icons/LoaderDisabled'
import { UploadDisabled } from '@common/icons/UploadDisabled'
import { TrashDisabled } from '@common/icons/TrashDisabled'
import { CollapseButton } from '@common/icons/CollapseButton'

export const SVG_NAMES = {
  PORTUGAL_FLAG: 'pt',
  GERMANY_FLAG: 'de',
  ITALY_FLAG: 'it',
  SPAIN_FLAG: 'es',
  NETHERLAND_FLAG: 'nl',
  FRANCE_FLAG: 'fr',
  SEARCH: 'search',
  CLEAR: 'clear',
  SEND: 'send',
  SUBJECT_ARROW: 'subject_arrow',
  INFO: 'info',
  UPLOAD: 'upload',
  UPLOAD_DISABLED: 'upload_disabled',
  TRASH: 'trash',
  TRASH_DISABLED: 'trash_disabled',
  LOADER: 'loader',
  LOADER_DISABLED: 'loader_disabled',
  DOWNLOAD: 'download',
  SEND_DISABLED: 'send_disabled',
  COLLAPSE_BUTTON: 'collapse_button',
}

export const ICONS = {
  [SVG_NAMES.FRANCE_FLAG]: FranceFlag,
  [SVG_NAMES.PORTUGAL_FLAG]: PortugalFlag,
  [SVG_NAMES.NETHERLAND_FLAG]: NetherlandFlag,
  [SVG_NAMES.GERMANY_FLAG]: GermanyFlag,
  [SVG_NAMES.SPAIN_FLAG]: SpainFlag,
  [SVG_NAMES.ITALY_FLAG]: ItalyFlag,
  [SVG_NAMES.SEARCH]: SearchIcon,
  [SVG_NAMES.CLEAR]: ClearIcon,
  [SVG_NAMES.SEND]: SendIcon,
  [SVG_NAMES.SUBJECT_ARROW]: SubjectArrow,
  [SVG_NAMES.INFO]: InfoIconBlueBg,
  [SVG_NAMES.UPLOAD]: UploadIcon,
  [SVG_NAMES.UPLOAD_DISABLED]: UploadDisabled,
  [SVG_NAMES.TRASH]: Trash,
  [SVG_NAMES.TRASH_DISABLED]: TrashDisabled,
  [SVG_NAMES.LOADER]: Loader,
  [SVG_NAMES.LOADER_DISABLED]: LoaderDisabled,
  [SVG_NAMES.DOWNLOAD]: DownloadIcon,
  [SVG_NAMES.SEND_DISABLED]: SendDisabled,
  [SVG_NAMES.COLLAPSE_BUTTON]: CollapseButton,
}

export type PathProps = {
  fill?: string
  stroke?: string
  strokeWidth?: string
  strokeMiterlimit?: string
}
