import { Chat } from '@common/types'

export const mockChatList: Chat[] = [
  {
    id: '9ef94b17-2ead-4824-8525-2e37f4080ca5',
    subject: 'BUYER.ORDERS.ORDER_TRACKING',
    customSubject: null,
    initiatorUserType: 'BUYER',
    lastMessageAt: '2025-05-22T17:37:18+00:00',
    lastMessageByUserType: 'BUYER',
    needsReply: true,
    overSLA: false,
    inactive: true,
    isSeen: true,
    createdAt: '2025-05-22T17:37:18+00:00',
    buyer: {
      id: 'a9801af2-54a7-4efa-a6a6-3cf1664dc096',
      lastName: 'Doe',
      firstName: 'John',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
      },
      accounts: [
        {
          id: '169bb2ef-5618-42d9-acdb-8b2160e2a947',
          firstName: '',
          lastName: '',
        },
        {
          id: '18461e1b-d845-4587-8ecb-90ba8838fc6e',
          firstName: 'Test',
          lastName: 'Metro',
        },
        {
          id: '1ebeca48-baa3-40a2-8421-a2b12162008d',
          firstName: 'Gert',
          lastName: 'Support',
        },
        {
          id: '52eb6be9-4285-440a-a4a2-731ac034d921',
          firstName: 'Seller',
          lastName: 'Edited Emp',
        },
        {
          id: '5b8dbd23-aac0-4ed7-abfe-a323ead4ed21',
          firstName: 'Seller',
          lastName: 'Test',
        },
        {
          id: 'c04f267d-ca73-4bfa-b161-031962f0868a',
          firstName: 'Test',
          lastName: 'Messages',
        },
        {
          id: 'cefcdbf4-f93e-4bc2-8a39-ff189b4b5b7d',
          firstName: 'test',
          lastName: 'PP',
        },
        {
          id: 'dddbfb7f-7df0-41c7-9fab-587b51bbba3e',
          firstName: 'Seller',
          lastName: 'Test message',
        },
        {
          id: 'e739e847-e734-4d85-8963-ba2318cbe9c8',
          firstName: 'Test',
          lastName: 'Nach Name',
        },
      ],
    },
    order: {
      id: 'cd67be7b-6d3d-45de-8aa8-15c5612691a3',
      orderNumber: 'O25-476446721693',
      salesChannel: 'de',
    },
  },
  {
    id: 'new',
    subject: 'BUYER.OTHERS',
    customSubject: 'Blablabla',
    initiatorUserType: 'BUYER',
    lastMessageAt: null,
    lastMessageByUserType: null,
    needsReply: false,
    overSLA: true,
    inactive: false,
    isSeen: true,
    createdAt: '2025-05-23T08:53:42+00:00',
    buyer: {
      id: '5bda4cf1-3113-441a-986f-19d89016ad0c',
      lastName: 'Taylor',
      firstName: 'Sam',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
      },
      accounts: [
        {
          id: '169bb2ef-5618-42d9-acdb-8b2160e2a947',
          firstName: '',
          lastName: '',
        },
        {
          id: '18461e1b-d845-4587-8ecb-90ba8838fc6e',
          firstName: 'Test',
          lastName: 'Metro',
        },
        {
          id: '1ebeca48-baa3-40a2-8421-a2b12162008d',
          firstName: 'Gert',
          lastName: 'Support',
        },
        {
          id: '52eb6be9-4285-440a-a4a2-731ac034d921',
          firstName: 'Seller',
          lastName: 'Edited Emp',
        },
        {
          id: '5b8dbd23-aac0-4ed7-abfe-a323ead4ed21',
          firstName: 'Seller',
          lastName: 'Test',
        },
        {
          id: 'c04f267d-ca73-4bfa-b161-031962f0868a',
          firstName: 'Test',
          lastName: 'Messages',
        },
        {
          id: 'cefcdbf4-f93e-4bc2-8a39-ff189b4b5b7d',
          firstName: 'test',
          lastName: 'PP',
        },
        {
          id: 'dddbfb7f-7df0-41c7-9fab-587b51bbba3e',
          firstName: 'Seller',
          lastName: 'Test message',
        },
        {
          id: 'e739e847-e734-4d85-8963-ba2318cbe9c8',
          firstName: 'Test',
          lastName: 'Nach Name',
        },
      ],
    },
    order: {
      id: '0bf63ec2-71e8-42ef-9e31-0b0256cf2ac9',
      orderNumber: 'O25-479889176657',
      salesChannel: 'pt',
    },
  },
  {
    id: 'f3a9a88e-5b3c-4b1e-9f0a-7e8d6c5b4a32',
    subject: 'BUYER.RETURNS.RETURN_REQUEST',
    customSubject: 'Needs reply but is also over SLA',
    initiatorUserType: 'BUYER',
    lastMessageAt: '2025-05-21T11:20:10+00:00',
    lastMessageByUserType: 'BUYER',
    needsReply: true,
    overSLA: true,
    inactive: false,
    isSeen: false,
    createdAt: '2025-05-21T11:20:10+00:00',
    buyer: {
      id: 'c1b2a3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6',
      lastName: 'Smith',
      firstName: 'Jane',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
      },
      accounts: [],
    },
    order: {
      id: 'a1b2c3d4-e5f6-a7b8-c9d0-e1f2a3b4c5d6',
      orderNumber: 'O25-************',
      salesChannel: 'fr',
    },
  },
  {
    id: 'b4c5d6e7-f8a9-b0c1-d2e3-f4a5b6c7d8e9',
    subject: 'BUYER.INVOICES.INVOICE_REQUEST',
    customSubject: 'A regular chat, nothing special',
    initiatorUserType: 'SELLER',
    lastMessageAt: '2025-05-24T09:00:00+00:00',
    lastMessageByUserType: 'SELLER',
    needsReply: false,
    overSLA: false,
    inactive: false,
    isSeen: true,
    createdAt: '2025-05-24T08:00:00+00:00',
    buyer: {
      id: 'd1e2f3a4-b5c6-d7e8-f9a0-b1c2d3e4f5a6',
      lastName: 'Jones',
      firstName: 'Alex',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
      },
      accounts: [],
    },
    order: {
      id: 'b1c2d3e4-f5a6-b7c8-d9e0-f1a2b3c4d5e6',
      orderNumber: 'O25-************',
      salesChannel: 'es',
    },
  },
]
