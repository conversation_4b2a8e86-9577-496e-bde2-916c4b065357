import { Chat } from '@common/types'

export const mockEmployeeChatList: Chat[] = [
  {
    id: '9efa85ea-f303-4e10-acb6-8aa68e244d36',
    subject: 'BUYER.INVOICES.INVOICE_CORRECTION',
    customSubject: null,
    initiatorUserType: 'BUYER',
    lastMessageAt: '2025-07-08T18:52:12+00:00',
    lastMessageByUserType: 'BUYER',
    needsReply: true,
    overSLA: false,
    inactive: false,
    isSeen: true,
    createdAt: '2025-05-23T08:17:37+00:00',
    buyer: {
      id: 'e41ed9ae-c587-48cd-9098-5c224488661c',
      lastName: 'Dumpty',
      firstName: 'Humpty',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
        name: 'My  shop DE 15\/02',
        userFriendlyId: 'SA21-********',
      },
      accounts: [
        {
          id: '169bb2ef-5618-42d9-acdb-8b2160e2a947',
          firstName: '',
          lastName: '',
        },
        {
          id: '18461e1b-d845-4587-8ecb-90ba8838fc6e',
          firstName: 'Test',
          lastName: 'Metro',
        },
        {
          id: '1ebeca48-baa3-40a2-8421-a2b12162008d',
          firstName: 'Gert',
          lastName: 'Support',
        },
        {
          id: '52eb6be9-4285-440a-a4a2-731ac034d921',
          firstName: 'Seller',
          lastName: 'Edited Emp',
        },
        {
          id: '5b8dbd23-aac0-4ed7-abfe-a323ead4ed21',
          firstName: 'Seller',
          lastName: 'Test',
        },
        {
          id: 'c04f267d-ca73-4bfa-b161-031962f0868a',
          firstName: 'Test',
          lastName: 'Messages',
        },
        {
          id: 'cefcdbf4-f93e-4bc2-8a39-ff189b4b5b7d',
          firstName: 'test',
          lastName: 'PP',
        },
        {
          id: 'dddbfb7f-7df0-41c7-9fab-587b51bbba3e',
          firstName: 'Seller',
          lastName: 'Test message',
        },
        {
          id: 'e739e847-e734-4d85-8963-ba2318cbe9c8',
          firstName: 'Test',
          lastName: 'Nach Name',
        },
      ],
    },
    order: {
      id: '7aea4e32-cc47-4833-bee1-c9294b4611f5',
      orderNumber: 'O25-479880954498',
      salesChannel: 'pt',
    },
  },
  {
    id: 'new',
    subject: 'SELLER.ORDERS.ORDER_CONFIRMATION',
    customSubject: null,
    initiatorUserType: 'SELLER',
    lastMessageAt: '2025-05-16T08:32:06+00:00',
    lastMessageByUserType: 'SELLER',
    needsReply: false,
    overSLA: false,
    inactive: true,
    isSeen: true,
    createdAt: '2025-05-16T08:32:06+00:00',
    buyer: {
      id: 'ccda3e9c-fb9c-4570-8cde-c64ebd643164',
      lastName: 'Luchianenco',
      firstName: 'Serghei',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
        name: 'My  shop DE 15\/02',
        userFriendlyId: 'SA21-********',
      },
      accounts: [
        {
          id: '169bb2ef-5618-42d9-acdb-8b2160e2a947',
          firstName: '',
          lastName: '',
        },
        {
          id: '18461e1b-d845-4587-8ecb-90ba8838fc6e',
          firstName: 'Test',
          lastName: 'Metro',
        },
        {
          id: '1ebeca48-baa3-40a2-8421-a2b12162008d',
          firstName: 'Gert',
          lastName: 'Support',
        },
        {
          id: '52eb6be9-4285-440a-a4a2-731ac034d921',
          firstName: 'Seller',
          lastName: 'Edited Emp',
        },
        {
          id: '5b8dbd23-aac0-4ed7-abfe-a323ead4ed21',
          firstName: 'Seller',
          lastName: 'Test',
        },
        {
          id: 'c04f267d-ca73-4bfa-b161-031962f0868a',
          firstName: 'Test',
          lastName: 'Messages',
        },
        {
          id: 'cefcdbf4-f93e-4bc2-8a39-ff189b4b5b7d',
          firstName: 'test',
          lastName: 'PP',
        },
        {
          id: 'dddbfb7f-7df0-41c7-9fab-587b51bbba3e',
          firstName: 'Seller',
          lastName: 'Test message',
        },
        {
          id: 'e739e847-e734-4d85-8963-ba2318cbe9c8',
          firstName: 'Test',
          lastName: 'Nach Name',
        },
      ],
    },
    order: {
      id: '300ad814-3dad-4d13-bd86-7d1924c6e15a',
      orderNumber: 'O25-473822061067',
      salesChannel: 'de',
    },
  },
  {
    id: '9eec7540-0b61-4b83-94d8-735739987e91',
    subject: 'BUYER.ORDERS.ORDER_TRACKING',
    customSubject: null,
    initiatorUserType: 'BUYER',
    lastMessageAt: '2025-05-16T08:29:26+00:00',
    lastMessageByUserType: 'BUYER',
    needsReply: true,
    overSLA: true,
    inactive: true,
    isSeen: true,
    createdAt: '2025-05-16T08:29:26+00:00',
    buyer: {
      id: '34ff6217-d4fa-4cb3-88c2-041da97d66b0',
      lastName: 'Alvarez',
      firstName: 'Roberto',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
        name: 'My  shop DE 15\/02',
        userFriendlyId: 'SA21-********',
      },
      accounts: [
        {
          id: '169bb2ef-5618-42d9-acdb-8b2160e2a947',
          firstName: '',
          lastName: '',
        },
        {
          id: '18461e1b-d845-4587-8ecb-90ba8838fc6e',
          firstName: 'Test',
          lastName: 'Metro',
        },
        {
          id: '1ebeca48-baa3-40a2-8421-a2b12162008d',
          firstName: 'Gert',
          lastName: 'Support',
        },
        {
          id: '52eb6be9-4285-440a-a4a2-731ac034d921',
          firstName: 'Seller',
          lastName: 'Edited Emp',
        },
        {
          id: '5b8dbd23-aac0-4ed7-abfe-a323ead4ed21',
          firstName: 'Seller',
          lastName: 'Test',
        },
        {
          id: 'c04f267d-ca73-4bfa-b161-031962f0868a',
          firstName: 'Test',
          lastName: 'Messages',
        },
        {
          id: 'cefcdbf4-f93e-4bc2-8a39-ff189b4b5b7d',
          firstName: 'test',
          lastName: 'PP',
        },
        {
          id: 'dddbfb7f-7df0-41c7-9fab-587b51bbba3e',
          firstName: 'Seller',
          lastName: 'Test message',
        },
        {
          id: 'e739e847-e734-4d85-8963-ba2318cbe9c8',
          firstName: 'Test',
          lastName: 'Nach Name',
        },
      ],
    },
    order: {
      id: '11f2354f-8526-4337-842d-17b5a9dd77ba',
      orderNumber: 'O25-473820073331',
      salesChannel: 'de',
    },
  },
  {
    id: '9ef94b17-2ead-4824-8525-2e37f4080ca5',
    subject: 'BUYER.ORDERS.ORDER_TRACKING',
    customSubject: null,
    initiatorUserType: 'BUYER',
    lastMessageAt: '2025-05-22T17:37:18+00:00',
    lastMessageByUserType: 'BUYER',
    needsReply: true,
    overSLA: false,
    inactive: false,
    isSeen: true,
    createdAt: '2025-05-22T17:37:18+00:00',
    buyer: {
      id: 'a9801af2-54a7-4efa-a6a6-3cf1664dc096',
      lastName: 'Doe',
      firstName: 'John',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
      },
      accounts: [
        {
          id: '169bb2ef-5618-42d9-acdb-8b2160e2a947',
          firstName: '',
          lastName: '',
        },
        {
          id: '18461e1b-d845-4587-8ecb-90ba8838fc6e',
          firstName: 'Test',
          lastName: 'Metro',
        },
        {
          id: '1ebeca48-baa3-40a2-8421-a2b12162008d',
          firstName: 'Gert',
          lastName: 'Support',
        },
        {
          id: '52eb6be9-4285-440a-a4a2-731ac034d921',
          firstName: 'Seller',
          lastName: 'Edited Emp',
        },
        {
          id: '5b8dbd23-aac0-4ed7-abfe-a323ead4ed21',
          firstName: 'Seller',
          lastName: 'Test',
        },
        {
          id: 'c04f267d-ca73-4bfa-b161-031962f0868a',
          firstName: 'Test',
          lastName: 'Messages',
        },
        {
          id: 'cefcdbf4-f93e-4bc2-8a39-ff189b4b5b7d',
          firstName: 'test',
          lastName: 'PP',
        },
        {
          id: 'dddbfb7f-7df0-41c7-9fab-587b51bbba3e',
          firstName: 'Seller',
          lastName: 'Test message',
        },
        {
          id: 'e739e847-e734-4d85-8963-ba2318cbe9c8',
          firstName: 'Test',
          lastName: 'Nach Name',
        },
      ],
    },
    order: {
      id: 'cd67be7b-6d3d-45de-8aa8-15c5612691a3',
      orderNumber: 'O25-476446721693',
      salesChannel: 'de',
    },
  },
  {
    id: '9efa92d2-6ec7-46ed-8c2b-b6a16596ed0b',
    subject: 'BUYER.OTHERS',
    customSubject: 'Blablabla',
    initiatorUserType: 'BUYER',
    lastMessageAt: '2025-05-23T10:47:50+00:00',
    lastMessageByUserType: 'BUYER',
    needsReply: false,
    overSLA: true,
    inactive: false,
    isSeen: true,
    createdAt: '2025-05-23T08:53:42+00:00',
    buyer: {
      id: '5bda4cf1-3113-441a-986f-19d89016ad0c',
      lastName: 'Taylor',
      firstName: 'Sam',
    },
    seller: {
      organization: {
        id: '581c41ea-6bc5-4716-a325-4a803c0ebff0',
        shopName: 'My DESHOP',
      },
      accounts: [
        {
          id: '169bb2ef-5618-42d9-acdb-8b2160e2a947',
          firstName: '',
          lastName: '',
        },
        {
          id: '18461e1b-d845-4587-8ecb-90ba8838fc6e',
          firstName: 'Test',
          lastName: 'Metro',
        },
        {
          id: '1ebeca48-baa3-40a2-8421-a2b12162008d',
          firstName: 'Gert',
          lastName: 'Support',
        },
        {
          id: '52eb6be9-4285-440a-a4a2-731ac034d921',
          firstName: 'Seller',
          lastName: 'Edited Emp',
        },
        {
          id: '5b8dbd23-aac0-4ed7-abfe-a323ead4ed21',
          firstName: 'Seller',
          lastName: 'Test',
        },
        {
          id: 'c04f267d-ca73-4bfa-b161-031962f0868a',
          firstName: 'Test',
          lastName: 'Messages',
        },
        {
          id: 'cefcdbf4-f93e-4bc2-8a39-ff189b4b5b7d',
          firstName: 'test',
          lastName: 'PP',
        },
        {
          id: 'dddbfb7f-7df0-41c7-9fab-587b51bbba3e',
          firstName: 'Seller',
          lastName: 'Test message',
        },
        {
          id: 'e739e847-e734-4d85-8963-ba2318cbe9c8',
          firstName: 'Test',
          lastName: 'Nach Name',
        },
      ],
    },
    order: {
      id: '0bf63ec2-71e8-42ef-9e31-0b0256cf2ac9',
      orderNumber: 'O25-479889176657',
      salesChannel: 'pt',
    },
  },
]
