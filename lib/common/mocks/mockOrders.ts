import { OrderLineStatus } from '@common/types'

export const mockOrders = [
  {
    orderNumber: 'O24-188920076217',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Alpha Widget',
        image: 'https://placehold.co/400',
        gtin: '111111111',
        sku: 'SKU-A1',
        mid: 'MID-A1',
        quantity: 1,
        status: OrderLineStatus.PLACED,
      },
      {
        name: 'Beta Gadget',
        image: 'https://placehold.co/150',
        gtin: '*********',
        sku: 'SKU-B2',
        mid: 'MID-B2',
        quantity: 2,
        status: OrderLineStatus.CANCELED,
      },
    ],
    buyerDetails: {
      firstName: 'John',
      lastName: 'Doe',
    },
    billingAddress: {
      city: 'City A',
      type: 'billing',
      street: 'Street A',
      country: 'us',
      lastName: 'Doe',
      firstName: 'John',
      postalCode: '12345',
      companyName: 'Company A',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State A',
      houseNumberOrName: '1',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City A',
      type: 'shipping',
      street: 'Street A',
      country: 'us',
      lastName: 'Doe',
      firstName: 'John',
      postalCode: '12345',
      companyName: 'Company A',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State A',
      houseNumberOrName: '1',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+1234567890',
    },
  },
  {
    orderNumber: 'O24-234567890123',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Gamma Device',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-C3',
        mid: 'MID-C3',
        quantity: 1,
        status: OrderLineStatus.CONFIRMED,
      },
    ],
    buyerDetails: {
      firstName: 'Jane',
      lastName: 'Smith',
    },
    billingAddress: {
      city: 'City B',
      type: 'billing',
      street: 'Street B',
      country: 'us',
      lastName: 'Smith',
      firstName: 'Jane',
      postalCode: '67890',
      companyName: 'Company B',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State B',
      houseNumberOrName: '2',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City B',
      type: 'shipping',
      street: 'Street B',
      country: 'us',
      lastName: 'Smith',
      firstName: 'Jane',
      postalCode: '67890',
      companyName: 'Company B',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State B',
      houseNumberOrName: '2',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+0987654321',
    },
  },
  {
    orderNumber: 'O24-987654321098',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Delta Apparatus',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-D4',
        mid: 'MID-D4',
        quantity: 1,
        status: OrderLineStatus.SHIPPED,
      },
      {
        name: 'Epsilon Instrument',
        image: 'https://placehold.co/300',
        gtin: '*********',
        sku: 'SKU-E5',
        mid: 'MID-E5',
        quantity: 3,
        status: OrderLineStatus.CANCELED,
      },
      {
        name: 'Zeta Machine',
        image: 'https://placehold.co/250',
        gtin: '*********',
        sku: 'SKU-F6',
        mid: 'MID-F6',
        quantity: 2,
        status: OrderLineStatus.RETURN_REQUEST,
      },
      {
        name: 'Eta Thing',
        image: 'https://placehold.co/250',
        gtin: '77777777',
        sku: 'SKU-F7',
        mid: 'MID-F7',
        quantity: 1,
        status: OrderLineStatus.RETURN_REQUESTED,
      },
    ],
    buyerDetails: {
      firstName: 'Alice',
      lastName: 'Johnson',
    },
    billingAddress: {
      city: 'City C',
      type: 'billing',
      street: 'Street C',
      country: 'us',
      lastName: 'Johnson',
      firstName: 'Alice',
      postalCode: '11223',
      companyName: 'Company C',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State C',
      houseNumberOrName: '3',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City C',
      type: 'shipping',
      street: 'Street C',
      country: 'us',
      lastName: 'Johnson',
      firstName: 'Alice',
      postalCode: '11223',
      companyName: 'Company C',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State C',
      houseNumberOrName: '3',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+1122334455',
    },
  },
  {
    orderNumber: 'O24-135792468013',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Theta Contraption',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-G7',
        mid: 'MID-G7',
        quantity: 1,
        status: OrderLineStatus.RETURN_ACCEPTED,
      },
      {
        name: 'Iota Mechanism',
        image: 'https://placehold.co/150',
        gtin: '*********',
        sku: 'SKU-H8',
        mid: 'MID-H8',
        quantity: 2,
        status: OrderLineStatus.RETURN_DECLINED,
      },
    ],
    buyerDetails: {
      firstName: 'Bob',
      lastName: 'Brown',
    },
    billingAddress: {
      city: 'City D',
      type: 'billing',
      street: 'Street D',
      country: 'us',
      lastName: 'Brown',
      firstName: 'Bob',
      postalCode: '33445',
      companyName: 'Company D',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State D',
      houseNumberOrName: '4',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City D',
      type: 'shipping',
      street: 'Street D',
      country: 'us',
      lastName: 'Brown',
      firstName: 'Bob',
      postalCode: '33445',
      companyName: 'Company D',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State D',
      houseNumberOrName: '4',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+2233445566',
    },
  },
  {
    orderNumber: 'O24-567890123456',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Kappa Gizmo',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-I9',
        mid: 'MID-I9',
        quantity: 1,
        status: OrderLineStatus.PENDING_VERIFICATION,
      },
    ],
    buyerDetails: {
      firstName: 'Charlie',
      lastName: 'Davis',
    },
    billingAddress: {
      city: 'City E',
      type: 'billing',
      street: 'Street E',
      country: 'us',
      lastName: 'Davis',
      firstName: 'Charlie',
      postalCode: '55667',
      companyName: 'Company E',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State E',
      houseNumberOrName: '5',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City E',
      type: 'shipping',
      street: 'Street E',
      country: 'us',
      lastName: 'Davis',
      firstName: 'Charlie',
      postalCode: '55667',
      companyName: 'Company E',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State E',
      houseNumberOrName: '5',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+3344556677',
    },
  },
  {
    orderNumber: 'O24-567890123457',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Lambda Tool',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-J10',
        mid: 'MID-J10',
        quantity: 2,
        status: OrderLineStatus.BLOCKED,
      },
      {
        name: 'Mu Instrument',
        image: 'https://placehold.co/150',
        gtin: '*********',
        sku: 'SKU-K11',
        mid: 'MID-K11',
        quantity: 1,
        status: OrderLineStatus.CONFIRMED,
      },
    ],
    buyerDetails: {
      firstName: 'David',
      lastName: 'Evans',
    },
    billingAddress: {
      city: 'City F',
      type: 'billing',
      street: 'Street F',
      country: 'us',
      lastName: 'Evans',
      firstName: 'David',
      postalCode: '77889',
      companyName: 'Company F',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State F',
      houseNumberOrName: '6',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City F',
      type: 'shipping',
      street: 'Street F',
      country: 'us',
      lastName: 'Evans',
      firstName: 'David',
      postalCode: '77889',
      companyName: 'Company F',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State F',
      houseNumberOrName: '6',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+4455667788',
    },
  },
  {
    orderNumber: 'O24-567890123458',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Nu Device',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-L12',
        mid: 'MID-L12',
        quantity: 1,
        status: OrderLineStatus.SHIPPED,
      },
      {
        name: 'Xi Gadget',
        image: 'https://placehold.co/300',
        gtin: '*********',
        sku: 'SKU-M13',
        mid: 'MID-M13',
        quantity: 4,
        status: OrderLineStatus.PLACED,
      },
      {
        name: 'Omicron Widget',
        image: 'https://placehold.co/250',
        gtin: '*********',
        sku: 'SKU-N14',
        mid: 'MID-N14',
        quantity: 2,
        status: OrderLineStatus.CONFIRMED,
      },
    ],
    buyerDetails: {
      firstName: 'Eve',
      lastName: 'Foster',
    },
    billingAddress: {
      city: 'City G',
      type: 'billing',
      street: 'Street G',
      country: 'us',
      lastName: 'Foster',
      firstName: 'Eve',
      postalCode: '99001',
      companyName: 'Company G',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State G',
      houseNumberOrName: '7',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City G',
      type: 'shipping',
      street: 'Street G',
      country: 'us',
      lastName: 'Foster',
      firstName: 'Eve',
      postalCode: '99001',
      companyName: 'Company G',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State G',
      houseNumberOrName: '7',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+5566778899',
    },
  },
  {
    orderNumber: 'O24-567890123459',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Pi Appliance',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-O15',
        mid: 'MID-O15',
        quantity: 1,
        status: OrderLineStatus.SHIPPED,
      },
    ],
    buyerDetails: {
      firstName: 'Frank',
      lastName: 'Green',
    },
    billingAddress: {
      city: 'City H',
      type: 'billing',
      street: 'Street H',
      country: 'us',
      lastName: 'Green',
      firstName: 'Frank',
      postalCode: '22334',
      companyName: 'Company H',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State H',
      houseNumberOrName: '8',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City H',
      type: 'shipping',
      street: 'Street H',
      country: 'us',
      lastName: 'Green',
      firstName: 'Frank',
      postalCode: '22334',
      companyName: 'Company H',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State H',
      houseNumberOrName: '8',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+6677889900',
    },
  },
  {
    orderNumber: 'O24-567890123460',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Rho Device',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-P16',
        mid: 'MID-P16',
        quantity: 1,
        status: OrderLineStatus.PLACED,
      },
      {
        name: 'Sigma Gadget',
        image: 'https://placehold.co/150',
        gtin: '*********',
        sku: 'SKU-Q17',
        mid: 'MID-Q17',
        quantity: 2,
        status: OrderLineStatus.SHIPPED,
      },
    ],
    buyerDetails: {
      firstName: 'Grace',
      lastName: 'Harris',
    },
    billingAddress: {
      city: 'City I',
      type: 'billing',
      street: 'Street I',
      country: 'us',
      lastName: 'Harris',
      firstName: 'Grace',
      postalCode: '44556',
      companyName: 'Company I',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State I',
      houseNumberOrName: '9',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City I',
      type: 'shipping',
      street: 'Street I',
      country: 'us',
      lastName: 'Harris',
      firstName: 'Grace',
      postalCode: '44556',
      companyName: 'Company I',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State I',
      houseNumberOrName: '9',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+7788990011',
    },
  },
  {
    orderNumber: 'O24-567890123461',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Tau Widget',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-R18',
        mid: 'MID-R18',
        quantity: 1,
        status: OrderLineStatus.SHIPPED,
      },
      {
        name: 'Upsilon Device',
        image: 'https://placehold.co/300',
        gtin: '*********',
        sku: 'SKU-S19',
        mid: 'MID-S19',
        quantity: 3,
        status: OrderLineStatus.PLACED,
      },
      {
        name: 'Phi Gadget',
        image: 'https://placehold.co/250',
        gtin: '*********',
        sku: 'SKU-T20',
        mid: 'MID-T20',
        quantity: 2,
        status: OrderLineStatus.SHIPPED,
      },
    ],
    buyerDetails: {
      firstName: 'Hank',
      lastName: 'Ivy',
    },
    billingAddress: {
      city: 'City J',
      type: 'billing',
      street: 'Street J',
      country: 'us',
      lastName: 'Ivy',
      firstName: 'Hank',
      postalCode: '66778',
      companyName: 'Company J',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State J',
      houseNumberOrName: '10',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City J',
      type: 'shipping',
      street: 'Street J',
      country: 'us',
      lastName: 'Ivy',
      firstName: 'Hank',
      postalCode: '66778',
      companyName: 'Company J',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State J',
      houseNumberOrName: '10',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+8899001122',
    },
  },
  {
    orderNumber: 'O24-567890123462',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Chi Appliance',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-U21',
        mid: 'MID-U21',
        quantity: 1,
        status: OrderLineStatus.SHIPPED,
      },
    ],
    buyerDetails: {
      firstName: 'Ivy',
      lastName: 'Jones',
    },
    billingAddress: {
      city: 'City K',
      type: 'billing',
      street: 'Street K',
      country: 'us',
      lastName: 'Jones',
      firstName: 'Ivy',
      postalCode: '88990',
      companyName: 'Company K',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State K',
      houseNumberOrName: '11',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City K',
      type: 'shipping',
      street: 'Street K',
      country: 'us',
      lastName: 'Jones',
      firstName: 'Ivy',
      postalCode: '88990',
      companyName: 'Company K',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State K',
      houseNumberOrName: '11',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+9900112233',
    },
  },
  {
    orderNumber: 'O24-567890123463',
    datePlaced: '2021-01-01',
    orderLines: [
      {
        name: 'Psi Device',
        image: 'https://placehold.co/400',
        gtin: '*********',
        sku: 'SKU-V22',
        mid: 'MID-V22',
        quantity: 1,
        status: OrderLineStatus.PLACED,
      },
      {
        name: 'Omega Gadget',
        image: 'https://placehold.co/150',
        gtin: '*********',
        sku: 'SKU-W23',
        mid: 'MID-W23',
        quantity: 2,
        status: OrderLineStatus.SHIPPED,
      },
    ],
    buyerDetails: {
      firstName: 'Jack',
      lastName: 'King',
    },
    billingAddress: {
      city: 'City L',
      type: 'billing',
      street: 'Street L',
      country: 'us',
      lastName: 'King',
      firstName: 'Jack',
      postalCode: '99012',
      companyName: 'Company L',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State L',
      houseNumberOrName: '12',
      destinationRegionId: null,
      freightForwardingPhone: null,
    },
    shippingAddress: {
      city: 'City L',
      type: 'shipping',
      street: 'Street L',
      country: 'us',
      lastName: 'King',
      firstName: 'Jack',
      postalCode: '99012',
      companyName: 'Company L',
      geoValidation: '',
      additionalInfo: '',
      stateOrProvince: 'State L',
      houseNumberOrName: '12',
      destinationRegionId: 'US_MAIN',
      freightForwardingPhone: '+0011223344',
    },
  },
]
