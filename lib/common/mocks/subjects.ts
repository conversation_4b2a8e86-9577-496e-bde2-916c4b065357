import { Subject } from '@common/types'

export const subjects: Subject[] = [
  {
    id: 111,
    subject: 'ORDERS',
    subjectText: 'MESSAGE_CENTER.SUBJECT.ORDERS',
    description: null,
    isCustom: false,
    parentId: null,
    children: [
      {
        id: 211,
        subject: 'ORDER_CONFIRMATION',
        subjectText: 'MESSAGE_CENTER.SUBJECT.ORDER_CONFIRMATION',
        description: null,
        isCustom: false,
        parentId: 111,
        children: [],
      },
      {
        id: 212,
        subject: 'DELIVERY_INSTRUCTIONS',
        subjectText: 'MESSAGE_CENTER.SUBJECT.DELIVERY_INSTRUCTIONS',
        description: null,
        isCustom: false,
        parentId: 111,
        children: [],
      },
      {
        id: 213,
        subject: 'ASSEMBLY_INSTRUCTIONS',
        subjectText: 'MESSAGE_CENTER.SUBJECT.ASSEMBLY_INSTRUCTIONS',
        description: null,
        isCustom: false,
        parentId: 111,
        children: [],
      },
      {
        id: 214,
        subject: 'ORDER_TRACKING',
        subjectText: 'MESSAGE_CENTER.SUBJECT.ORDER_TRACKING',
        description: 'To provide helpful info for customer',
        isCustom: false,
        parentId: 111,
        children: [],
      },
      {
        id: 215,
        subject: 'DELAYED_DELIVERY',
        subjectText: 'MESSAGE_CENTER.SUBJECT.DELAYED_DELIVERY',
        description: 'To inform customer of problems',
        isCustom: false,
        parentId: 111,
        children: [],
      },
      {
        id: 216,
        subject: 'ORDER_CANCELLATION',
        subjectText: 'MESSAGE_CENTER.SUBJECT.ORDER_CANCELLATION',
        description: null,
        isCustom: false,
        parentId: 111,
        children: [],
      },
    ],
  },
  {
    id: 121,
    subject: 'INVOICES',
    subjectText: 'MESSAGE_CENTER.SUBJECT.INVOICES',
    description: null,
    isCustom: false,
    parentId: null,
    children: [
      {
        id: 221,
        subject: 'INVOICE_INSTRUCTIONS',
        subjectText: 'MESSAGE_CENTER.SUBJECT.INVOICE_INSTRUCTIONS',
        description: 'To provide helpful info for customer to make payment',
        isCustom: false,
        parentId: 121,
        children: [],
      },
      {
        id: 222,
        subject: 'INVOICE_ATTACHMENT',
        subjectText: 'MESSAGE_CENTER.SUBJECT.INVOICE_ATTACHMENT',
        description: null,
        isCustom: false,
        parentId: 121,
        children: [],
      },
    ],
  },
  {
    id: 131,
    subject: 'OTHERS',
    subjectText: 'MESSAGE_CENTER.SUBJECT.OTHERS',
    description: null,
    isCustom: true,
    parentId: null,
    children: [],
  },
]
