import { ChatBadgeStatus } from '@common/constants'

export type SalesChannel = 'pt' | 'de' | 'es' | 'it' | 'nl' | 'fr'
export type UserRoleType = 'BUYER' | 'SELLER' | 'EMPLOYEE'

export interface Chat {
  id: string
  subject: string
  customSubject?: string | null
  status?: ChatBadgeStatus
  createdAt: string
  isSelected?: boolean
  initiatorUserType: UserRoleType
  isSeen: boolean
  buyer: User
  seller: Seller
  order: Order
  lastMessageAt: string | null
  lastMessageByUserType: UserRoleType | null
  needsReply: boolean
  overSLA: boolean
  inactive: boolean
}

export interface User {
  id?: string
  firstName: string
  lastName: string
}

export interface Seller {
  accounts: User[]
  organization: Organization
}

export interface Organization {
  id: string
  name?: string
  shopName: string
  userFriendlyId?: string
}

interface Order {
  id: string
  orderNumber: string
  salesChannel: SalesChannel
}
