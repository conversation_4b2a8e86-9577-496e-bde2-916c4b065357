import { User } from '@common/types/chat.type'

export interface Message {
  id: string
  chatId?: string
  senderId: string
  senderUserType?: string
  receiverId?: string
  content: string
  seenAt?: string | null
  createdAt: string
  attachments?: Attachment[]
  sender?: User
}

export interface Attachment {
  fileId: string
  fileName: string
  fileType: string
  fileSize?: number
  downloadUrl?: string
}

export interface SignedUrl {
  id: string
  fileId: string
  signedUrl: string
}

export interface MessageToSave {
  content: string
  attachments?: Attachment[]
}
