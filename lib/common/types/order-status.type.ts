export enum OrderStatus {
  CANCELED = 'CANCELED',
  CONFIRMED = 'CONFIRMED',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  WAITING_FOR_APPROVAL = 'WAITING_FOR_APPROVAL',
  MIXED = 'MIXED',
  WAITING_FOR_PAYMENT = 'WAITING_FOR_PAYMENT',
  PAID = 'PAID',
}

export enum OrderLineStatus {
  PLACED = 'PLACED',
  CANCELED = 'CANCELED',
  CONFIRMED = 'CONFIRMED',
  SHIPPED = 'SHIPPED',
  WAITING_FOR_PAYMENT = 'WAITING_FOR_PAYMENT',
  RETURN_REQUEST = 'RETURN_REQUEST',
  RETURN_REQUESTED = 'RETURN_REQUESTED',
  RETURN_ACCEPTED = 'RETURN_ACCEPTED',
  RETURN_DECLINED = 'RETURN_DECLINED',
  PAID = 'PAID',
  PENDING_VERIFICATION = 'PENDING_VERIFICATION',
  BLOCKED = 'BLOCKED',
}
