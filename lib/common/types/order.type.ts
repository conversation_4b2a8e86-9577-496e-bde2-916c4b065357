import { OrderLineStatus } from '@common/types/order-status.type'

export interface Address {
  city: string
  type: string
  street: string
  country: string
  lastName: string
  firstName: string
  postalCode: string
  companyName: string
  geoValidation?: string
  additionalInfo?: string
  stateOrProvince: string | null
  houseNumberOrName: string
  destinationRegionId?: string | null
  freightForwardingPhone?: string | null
}

export interface OrderItemType {
  id: string
  name: string
  image: string
  gtin: string
  sku: string | null
  mid: string
  quantity: number
  status: OrderLineStatus
}

export interface BuyerDetails {
  id: string
  firstName: string
  lastName: string
}

export interface SellerOrganization {
  id: string
  shopName: string
}

export interface OrderSeller {
  organization: SellerOrganization
}

export interface Order {
  orderId: string
  orderNumber: string
  salesChannel: string
  stateMachineStatus: string
  placedAt: string
  orderLines: OrderItemType[]
  buyer: BuyerDetails
  seller: OrderSeller
  billingAddress: Address
  shippingAddress: Address
}
