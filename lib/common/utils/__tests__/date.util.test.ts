import dayjs from 'dayjs'
import {
  parseDate,
  isInputDateFormat,
  formatForScreenReader,
  dateFormat,
  timeFormat,
} from '../date.util'
import { isEmpty } from '../type.util'

vi.mock('../type.util', () => ({
  isEmpty: vi.fn(),
}))

const mockTime: string = '2024-11-22T15:33:55+00:00'
describe('parseDate', () => {
  it('returns empty string if value is empty', () => {
    isEmpty.mockReturnValue(true)
    expect(parseDate('')).toBe('')
  })

  it('returns value if it matches input date format', () => {
    isEmpty.mockReturnValue(false)
    expect(parseDate('12.03.2024')).toBe('12.03.2024')
  })

  it('parses valid date and formats it', () => {
    isEmpty.mockReturnValue(false)
    expect(parseDate(mockTime)).toBe(dayjs(mockTime).format(dateFormat))
  })

  it('returns original value if date is invalid', () => {
    isEmpty.mockReturnValue(false)
    expect(parseDate('invalid-date')).toBe('invalid-date')
  })

  it('parses a valid ISO date and formats it to the provided time format', () => {
    const expectedTime = dayjs(mockTime).format(timeFormat)
    expect(parseDate(mockTime, timeFormat)).toBe(expectedTime)
  })
})

describe('isInputDateFormat', () => {
  it('returns true for valid date format', () => {
    expect(isInputDateFormat('12.03.2024')).toBe(true)
  })

  it('returns false for invalid date format', () => {
    expect(isInputDateFormat('2024-03-12')).toBe(false)
  })

  it('returns false for completely incorrect input', () => {
    expect(isInputDateFormat('invalid-date')).toBe(false)
  })
})

describe('formatForScreenReader', () => {
  const mockDate = '2024-11-22T15:33:55+00:00'
  const format = 'dddd, D MMMM YYYY [at] HH:mm'

  beforeEach(() => {
    isEmpty.mockReset()
  })

  it('returns empty string if value is empty', () => {
    isEmpty.mockReturnValue(true)
    const result = formatForScreenReader('', 'en', format)
    expect(result).toBe('')
  })

  it('returns empty string if date is invalid', () => {
    isEmpty.mockReturnValue(false)
    const result = formatForScreenReader('invalid-date', 'en', format)
    expect(result).toBe('')
  })

  it('formats date correctly in default English locale', () => {
    isEmpty.mockReturnValue(false)
    const result = formatForScreenReader(mockDate, 'en', format)
    expect(result).toBe(dayjs(mockDate).locale('en').format(format))
  })

  it('formats date correctly in French locale', () => {
    isEmpty.mockReturnValue(false)
    const result = formatForScreenReader(mockDate, 'fr', format)
    expect(result).toBe(dayjs(mockDate).locale('fr').format(format))
  })

  it('uses default locale if none provided', () => {
    isEmpty.mockReturnValue(false)
    const result = formatForScreenReader(mockDate, undefined, format)
    expect(result).toBe(dayjs(mockDate).locale('en').format(format))
  })
})
