import { isElementInViewport } from '../element-position.util'

describe('isElementInViewport', () => {
  let container: HTMLElement
  let child: HTMLElement

  beforeEach(() => {
    document.body.innerHTML = `
      <div class="scroll-container" style="height: 200px; overflow-y: scroll;">
        <div class="child" style="height: 100px;"></div>
      </div>
    `

    container = document.querySelector('.scroll-container') as HTMLElement
    child = document.querySelector('.child') as HTMLElement

    document.body.appendChild(container)

    vi.spyOn(child, 'closest').mockImplementation((selector: string) => {
      return selector === '.scroll-container' ? container : null
    })
  })

  it('returns true when element is fully within the container viewport', () => {
    vi.spyOn(child, 'getBoundingClientRect').mockReturnValue({
      top: 110,
      bottom: 150,
      left: 0,
      right: 0,
      width: 100,
      height: 40,
      x: 0,
      y: 0,
      toJSON: () => {},
    } as DOMRect)

    vi.spyOn(container, 'getBoundingClientRect').mockReturnValue({
      top: 100,
      bottom: 200,
      left: 0,
      right: 0,
      width: 100,
      height: 100,
      x: 0,
      y: 0,
      toJSON: () => {},
    } as DOMRect)

    expect(isElementInViewport(child, 'scroll-container')).toBe(true)
  })

  it('returns false when element is partially out of view', () => {
    vi.spyOn(child, 'getBoundingClientRect').mockReturnValue({
      top: 190,
      bottom: 210,
      left: 0,
      right: 0,
      width: 100,
      height: 20,
      x: 0,
      y: 0,
      toJSON: () => {},
    } as DOMRect)

    vi.spyOn(container, 'getBoundingClientRect').mockReturnValue({
      top: 100,
      bottom: 200,
      left: 0,
      right: 0,
      width: 100,
      height: 100,
      x: 0,
      y: 0,
      toJSON: () => {},
    } as DOMRect)

    expect(isElementInViewport(child, 'scroll-container')).toBe(false)
  })

  it('returns false when container is not found', () => {
    vi.spyOn(child, 'closest').mockReturnValue(null)

    expect(isElementInViewport(child, 'non-existent-class')).toBe(false)
  })
})
