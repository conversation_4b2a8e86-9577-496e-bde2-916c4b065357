import { describe, it, expect } from 'vitest'
import { convertFileSizeToBytes, isFileSizeExceeded } from '../fileSize'

describe('fileSize utils', () => {
  describe('convertFileSizeToBytes', () => {
    it('converts KB to bytes correctly', () => {
      expect(convertFileSizeToBytes('1KB')).toBe(1024)
      expect(convertFileSizeToBytes('10KB')).toBe(10240)
    })

    it('converts MB to bytes correctly', () => {
      expect(convertFileSizeToBytes('1MB')).toBe(1048576) // 1024 * 1024
      expect(convertFileSizeToBytes('5MB')).toBe(5242880) // 5 * 1024 * 1024
    })

    it('converts GB to bytes correctly', () => {
      expect(convertFileSizeToBytes('1GB')).toBe(1073741824) // 1024 * 1024 * 1024
      expect(convertFileSizeToBytes('2GB')).toBe(2147483648) // 2 * 1024 * 1024 * 1024
    })

    it('is case insensitive', () => {
      expect(convertFileSizeToBytes('5kb')).toBe(5120)
      expect(convertFileSizeToBytes('5Kb')).toBe(5120)
      expect(convertFileSizeToBytes('5KB')).toBe(5120)
    })

    it('returns Infinity for invalid formats', () => {
      expect(convertFileSizeToBytes('invalid')).toBe(Infinity)
      expect(convertFileSizeToBytes('5XB')).toBe(Infinity)
      expect(convertFileSizeToBytes('5')).toBe(Infinity)
      expect(convertFileSizeToBytes('')).toBe(Infinity)
    })
  })

  describe('isFileSizeExceeded', () => {
    it('returns true when file size exceeds the limit', () => {
      const file = new File(['a'.repeat(2048)], 'test.txt', {
        type: 'text/plain',
      })
      expect(isFileSizeExceeded(file, '1KB')).toBe(true)
    })

    it('returns false when file size is within the limit', () => {
      const file = new File(['a'.repeat(512)], 'test.txt', {
        type: 'text/plain',
      })
      expect(isFileSizeExceeded(file, '1KB')).toBe(false)
    })

    it('returns false when file size equals the limit', () => {
      const file = new File(['a'.repeat(1024)], 'test.txt', {
        type: 'text/plain',
      })
      expect(isFileSizeExceeded(file, '1KB')).toBe(false)
    })
  })
})
