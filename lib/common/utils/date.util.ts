import dayjs from 'dayjs'
import { isEmpty } from './type.util'
import 'dayjs/locale/en'
import 'dayjs/locale/fr'
import 'dayjs/locale/de'
import 'dayjs/locale/es'
import 'dayjs/locale/it'
import 'dayjs/locale/pt'

export const dateFormat = 'DD.MM.YYYY'
export const timeFormat = 'HH:mm'
export const DATE_FORMAT_REGEX =
  /^([0-2][0-9]|(3)[0-1])(\.)(((0)[0-9])|((1)[0-2]))(\.)\d{4}$/i

export const parseDate = (value: string, format?: string) => {
  if (isEmpty(value)) {
    return ''
  }
  if (isInputDateFormat(value)) {
    return value
  }
  if (dayjs(value).isValid()) {
    return dayjs(value).format(format ?? dateFormat)
  }
  return value
}

export const isInputDateFormat = (value: string) =>
  DATE_FORMAT_REGEX.test(value)

export const formatForScreenReader = (
  value: string,
  locale: string = 'en',
  format: string
): string => {
  if (isEmpty(value) || !dayjs(value).isValid()) return ''

  return dayjs(value).locale(locale).format(format)
}
