/**
 * Converts a file size string (e.g., "10MB") to bytes
 * @param fileSizeStr - File size string in format like "10MB", "5KB", "2GB"
 * @returns Size in bytes or Infinity if the format is invalid
 */
export const convertFileSizeToBytes = (fileSizeStr: string): number => {
  const sizeRegex = /^(\d+)(KB|MB|GB)$/i
  const match = fileSizeStr.match(sizeRegex)

  if (!match) {
    return Infinity
  }

  const [, size, unit] = match
  const sizeNum = parseInt(size, 10)

  switch (unit.toUpperCase()) {
    case 'KB':
      return sizeNum * 1024
    case 'MB':
      return sizeNum * 1024 * 1024
    case 'GB':
      return sizeNum * 1024 * 1024 * 1024
    default:
      return Infinity
  }
}

/**
 * Checks if a file exceeds the maximum allowed size
 * @param file - The file to check
 * @param maxFileSizeStr - Maximum file size as a string (e.g., "10MB")
 * @returns True if the file exceeds the maximum size, false otherwise
 */
export const isFileSizeExceeded = (
  file: File,
  maxFileSizeStr: string
): boolean => {
  const maxSizeInBytes = convertFileSizeToBytes(maxFileSizeStr)
  return file.size > maxSizeInBytes
}
