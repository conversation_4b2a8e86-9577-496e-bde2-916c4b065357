const OBJECT_TYPE_LENGTH = 8
export const objectType = (obj: any): string =>
  Object.prototype.toString.call(obj).slice(OBJECT_TYPE_LENGTH, -1)
export const isNumber = (param: any): boolean => typeof param === 'number'
export const isBoolean = (param: any): boolean =>
  objectType(param) === 'Boolean'
export const isString = (param: any): boolean => typeof param === 'string'
export const isArray = (param: any): boolean => Array.isArray(param)
export const isObject = (param: any): boolean => objectType(param) === 'Object'
export const isEmpty = (value: any): boolean => {
  if (!value && value !== 0) {
    return true
  }
  if (isNumber(value)) {
    return false
  }
  if (isString(value)) {
    return !value.trim()
  }
  if (isObject(value) && value) {
    return !Object.keys(value).length
  }
  if (isArray(value)) {
    return !value.length
  }
  return true
}
