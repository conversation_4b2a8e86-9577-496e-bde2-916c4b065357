import { parseDate } from '@common/utils/date.util'
import classNames from 'classnames'
import { SalesChannelBadge } from '@common/components/SalesChannelBadge'
import { AppVariant, Chat } from '@common/types'
import { useSubject } from '@common/hooks/useSubject'
import { ChatStatusBadges } from '@common/components/Badge/ChatStatusBadges'

interface Props {
  chat: Chat
  onClick: () => void
}
export const EmployeeChatCard = ({ chat, onClick }: Props): JSX.Element => {
  const { getSubject } = useSubject()
  return (
    <div
      className={classNames(
        'mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16',
        'mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full',
        { 'mc-bg-blue-tint-95': chat.isSelected }
      )}
      tabIndex={0}
      onClick={onClick}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick()
        }
      }}
      data-testid={`chat-card-${chat.id}`}
    >
      <div className="mc-flex mc-flex-row mc-flex-wrap mc-items-start">
        <span className="mc-mr-10">{chat.order.orderNumber}</span>
        <SalesChannelBadge salesChannel={chat.order.salesChannel} />
      </div>
      <div className="mc-leading-6 mc-text-regular margin mc-mt-15">
        <p>
          Buyer: {chat.buyer?.firstName} {chat.buyer?.lastName}
        </p>
        <p>Seller: {chat.seller.organization.shopName}</p>
        <p className="mc-mt-4 mc-break-words">{getSubject(chat)}</p>
      </div>
      <div className="mc-flex mc-justify-between mc-mt-16">
        <div className="mc-flex mc-items-center mc-gap-2">
          <ChatStatusBadges chat={chat} variant={AppVariant.Employee} />
        </div>
        {chat.lastMessageAt && (
          <time
            dateTime={new Date(chat.lastMessageAt).toISOString()}
            className="leading-5 mc-font-normal mc-text-base"
          >
            {parseDate(chat.lastMessageAt)}
          </time>
        )}
      </div>
    </div>
  )
}
