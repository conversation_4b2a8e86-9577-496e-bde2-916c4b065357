import { Container } from '@common/components/'
import { FC, useEffect, useRef, useState } from 'react'
import { EmployeeChatCard } from './EmployeeChatCard'
import { SearchChat } from '@common/components/SearchChat/SearchChat'
import { NoSearchResult } from '@common/components/NoSearchResult/NoSearchResult'
import classNames from 'classnames'
import { isElementInViewport } from '@common/utils/element-position.util'
import { Chat } from '@common/types'

interface Props {
  chats: Chat[]
  onSearch: (searchText: string) => void
  onSelectChat: (chat: Chat) => void
  selectedChat?: Chat
  children?: JSX.Element
  orderNumber?: string
}

export const EmployeeChatList: FC<Props> = ({
  chats,
  onSelectChat,
  onSearch,
  selectedChat,
  children,
  orderNumber,
}) => {
  const [searchText, setSearchText] = useState(orderNumber || '')
  const noSearchResult = searchText !== '' && chats.length === 0
  const chatRefs = useRef<Record<string, HTMLDivElement | null>>({})

  useEffect(() => {
    if (orderNumber !== undefined) {
      setSearchText(orderNumber)
    }
  }, [orderNumber])

  useEffect(() => {
    if (searchText !== orderNumber) {
      onSearch(searchText)
    }
  }, [searchText])

  useEffect(() => {
    if (selectedChat?.id) {
      scrollToChat(selectedChat.id)
    }
  }, [selectedChat])

  const scrollToChat = (chatId: string) => {
    const el = chatRefs.current[chatId]
    if (el && !isElementInViewport(el, 'mc-overflow-y-scroll')) {
      el.scrollIntoView({ behavior: 'auto', block: 'start' })
    }
  }

  if (!chats) {
    return <>{children}</>
  }

  return (
    <Container>
      <div className="mc-sticky mc-top-[0px]">
        <SearchChat onChange={setSearchText} value={searchText} />
        {noSearchResult && <NoSearchResult searchText={searchText} />}
      </div>
      <div
        className={classNames('mc-overflow-y-scroll', {
          'mc-h-[calc(100%-55px)]': chats.length > 0,
          'mc-h-auto': chats.length === 0,
        })}
      >
        {chats.map((chat) => (
          <div
            key={chat.id}
            ref={(el) => {
              chatRefs.current[chat.id] = el
            }}
            data-testid={`chat-wrapper-${chat.id}`}
          >
            <EmployeeChatCard
              onClick={() => onSelectChat(chat)}
              key={chat.id}
              chat={chat}
            />
          </div>
        ))}
        {children}
      </div>
    </Container>
  )
}
