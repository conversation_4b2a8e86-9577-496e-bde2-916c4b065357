import { fireEvent, render, screen } from '@testing-library/react'
import { describe, it, expect, vi } from 'vitest'
import { EmployeeChatCard } from '../EmployeeChatCard'
import { ChatBadgeStatus } from '../../../../common/constants'
import { Chat } from '@common/types'

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

const mockChat: Chat = {
  id: 'chat-001',
  subject: 'Subject',
  customSubject: null,
  status: ChatBadgeStatus.NEW,
  createdAt: '2025-06-15T12:00:00Z',
  isSelected: false,
  initiatorUserType: 'BUYER',
  isSeen: true,
  buyer: {
    id: 'b1',
    firstName: 'Alice',
    lastName: 'Smith',
  },
  seller: {
    accounts: [{ id: 's1', firstName: 'Bob', lastName: '<PERSON>' }],
    organization: {
      id: 'org1',
      shopName: 'WidgetWorks',
    },
  },
  order: {
    id: 'o123',
    orderNumber: 'ORD-12345',
    salesChannel: 'DE',
  },
  lastMessageAt: '2025-06-17T14:45:00Z',
  lastMessageByUserType: 'BUYER',
  needsReply: true,
  overSLA: false,
  inactive: false,
}
describe('EmployeeChatCard', () => {
  it('renders all relevant chat details', () => {
    const handleClick = vi.fn()
    render(<EmployeeChatCard chat={mockChat} onClick={handleClick} />)

    expect(screen.getByTestId('chat-card-chat-001')).toBeInTheDocument()
    expect(screen.getByText('Subject')).toBeInTheDocument()
    expect(screen.getByText('Buyer: Alice Smith')).toBeInTheDocument()
    expect(screen.getByText('Seller: WidgetWorks')).toBeInTheDocument()
    expect(screen.getByText('ORD-12345')).toBeInTheDocument()
    expect(screen.getByText('17.06.2025')).toBeInTheDocument()
  })

  it('calls onClick when clicked', () => {
    const handleClick = vi.fn()
    render(<EmployeeChatCard chat={mockChat} onClick={handleClick} />)

    const card = screen.getByTestId('chat-card-chat-001')
    fireEvent.click(card)

    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('does not render the time when lastMessageAt is not available', () => {
    const handleClick = vi.fn()
    const noTimeChat = { ...mockChat, lastMessageAt: null }
    render(<EmployeeChatCard chat={noTimeChat} onClick={handleClick} />)

    expect(screen.queryByRole('time')).not.toBeInTheDocument()
  })

  it.each([{ key: 'Enter' }, { key: ' ' }])(
    'calls onClick when $key key is pressed',
    ({ key }) => {
      const handleClick = vi.fn()
      render(<EmployeeChatCard chat={mockChat} onClick={handleClick} />)

      const card = screen.getByTestId('chat-card-chat-001')
      fireEvent.keyDown(card, { key })

      expect(handleClick).toHaveBeenCalledTimes(1)
    }
  )
})
