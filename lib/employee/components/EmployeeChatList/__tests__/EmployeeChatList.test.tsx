import { describe, expect, it, vi } from 'vitest'
import { fireEvent, render, screen } from '@testing-library/react'
import { EmployeeChatList } from '../EmployeeChatList'
import { mockEmployeeChatList } from '../../../../common/mocks/employee-chatlist'
import { ChatBadgeStatus } from '../../../../common/constants'
import { parseDate } from '../../../../common/utils/date.util'

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

vi.mock('@common/hooks/useSubject', () => ({
  useSubject: () => ({
    getSubject: (chat) => chat.customSubject ?? chat.subject,
    translatedSubjects: [],
  }),
}))

const mockChats = [
  {
    id: 'chat-1',
    subject: 'BUYER.RETURNS_REFUNDS.RETURN_STATUS',
    customSubject: null,
    status: ChatBadgeStatus.NEW,
    createdAt: '2025-06-15T09:00:00Z',
    isSelected: true,
    initiatorUserType: 'BUYER',
    isSeen: true,
    buyer: {
      id: 'b1',
      firstName: 'Alice',
      lastName: 'Smith',
    },
    seller: {
      accounts: [{ id: 's1', firstName: 'Bob', lastName: 'Jones' }],
      organization: {
        id: 'org1',
        shopName: 'ShopOne',
      },
    },
    order: {
      id: 'o123',
      orderNumber: 'ORD-001',
      salesChannel: 'ONLINE',
    },
    lastMessageAt: '2025-06-17T10:00:00Z',
    lastMessageByUserType: 'BUYER',
    needsReply: true,
    overSLA: false,
    inactive: false,
  },
  {
    id: 'chat-2',
    subject: 'BUYER.OTHERS',
    customSubject: 'Urgent Help Needed',
    status: ChatBadgeStatus.NEW,
    createdAt: '2025-06-14T11:00:00Z',
    isSelected: false,
    initiatorUserType: 'BUYER',
    isSeen: false,
    buyer: {
      id: 'b2',
      firstName: 'Eve',
      lastName: 'Adams',
    },
    seller: {
      accounts: [{ id: 's2', firstName: 'Carl', lastName: 'White' }],
      organization: {
        id: 'org2',
        shopName: 'ShopTwo',
      },
    },
    order: {
      id: 'o124',
      orderNumber: 'ORD-002',
      salesChannel: 'OFFLINE',
    },
    lastMessageAt: '2025-06-18T11:30:00Z',
    lastMessageByUserType: 'BUYER',
    needsReply: false,
    overSLA: true,
    inactive: true,
  },
]

describe('EmployeeChatList', () => {
  it('renders correctly', () => {
    const onSelectChat = vi.fn()
    const onSearch = vi.fn()
    const { container } = render(
      <EmployeeChatList
        chats={mockEmployeeChatList}
        onSelectChat={onSelectChat}
        onSearch={onSearch}
      />
    )
    expect(container).toMatchSnapshot()
  })

  it('renders all chats and their details', () => {
    const handleSelectChat = vi.fn()
    const handleSearch = vi.fn()
    render(
      <EmployeeChatList
        chats={mockChats}
        onSelectChat={handleSelectChat}
        onSearch={handleSearch}
      />
    )

    mockChats.forEach((chat) => {
      expect(screen.getByTestId(`chat-card-${chat.id}`)).toBeInTheDocument()
      expect(
        screen.getByText(parseDate(chat.lastMessageAt))
      ).toBeInTheDocument()
      expect(
        screen.getByText(
          `Buyer: ${chat.buyer.firstName} ${chat.buyer.lastName}`
        )
      ).toBeInTheDocument()
      expect(
        screen.getByText(`Seller: ${chat.seller.organization.shopName}`)
      ).toBeInTheDocument()
      expect(screen.getByText(chat.order.orderNumber)).toBeInTheDocument()
    })
  })

  it('renders customSubject over subject if available', () => {
    const handleSelectChat = vi.fn()
    const handleSearch = vi.fn()
    render(
      <EmployeeChatList
        chats={mockChats}
        onSelectChat={handleSelectChat}
        onSearch={handleSearch}
      />
    )

    expect(screen.getByText(mockChats[0].subject)).toBeInTheDocument()

    expect(screen.getByText(mockChats[1].customSubject)).toBeInTheDocument()
  })
  it('renders chats and triggers onSelectChat when clicked', () => {
    const handleSelectChat = vi.fn()
    const handleSearch = vi.fn()

    render(
      <EmployeeChatList
        chats={mockChats}
        onSelectChat={handleSelectChat}
        onSearch={handleSearch}
      />
    )

    const chatCard = screen.getByTestId('chat-card-chat-1')
    fireEvent.click(chatCard)

    expect(handleSelectChat).toHaveBeenCalledTimes(1)
    expect(handleSelectChat).toHaveBeenCalledWith(mockChats[0])
  })
  it('triggers onSearch when typing in search input', () => {
    const handleSearch = vi.fn()
    render(
      <EmployeeChatList
        chats={mockChats}
        onSelectChat={() => {}}
        onSearch={handleSearch}
      />
    )

    const searchInput = screen.getByPlaceholderText(
      'MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER'
    )
    fireEvent.change(searchInput, { target: { value: 'ORD-001' } })

    expect(handleSearch).toHaveBeenCalledTimes(1)
  })
})
