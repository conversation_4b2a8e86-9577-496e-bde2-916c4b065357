// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`EmployeeChatList > renders correctly 1`] = `
<div>
  <div
    class="mc-rounded-16 mc-bg-white-main mc-h-full mc-box-border mc-p-8 mc-font-lato mc-w-full"
  >
    <div
      class="mc-sticky mc-top-[0px]"
    >
      <div
        aria-label="MESSAGE_CENTER.CHATS.FILTERS.SEARCH.TEXT"
        class="mc-relative mc-overflow-hidden"
        role="search"
      >
        <svg
          aria-hidden="true"
          class="mc-block mc-align-middle mc-absolute mc-top-10 mc-left-10"
          data-testid="search-icon"
          fill=""
          height="1.25rem"
          viewBox="0 0 20 20"
          width="1.25rem"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <path
            clip-rule="evenodd"
            d="M8.75 14.375C5.64813 14.375 3.125 11.8512 3.125 8.75C3.125 5.64875 5.64813 3.125 8.75 3.125C11.8519 3.125 14.375 5.64875 14.375 8.75C14.375 11.8512 11.8519 14.375 8.75 14.375ZM17.9419 17.0581L14.1919 13.3081C14.135 13.2512 14.0688 13.2106 13.9994 13.18C15.0113 11.9825 15.625 10.4375 15.625 8.75C15.625 4.95937 12.5406 1.875 8.75 1.875C4.95937 1.875 1.875 4.95937 1.875 8.75C1.875 12.5406 4.95937 15.625 8.75 15.625C10.4375 15.625 11.9825 15.0113 13.18 13.9994C13.2106 14.0688 13.2512 14.135 13.3081 14.1919L17.0581 17.9419C17.18 18.0638 17.34 18.125 17.5 18.125C17.66 18.125 17.82 18.0638 17.9419 17.9419C18.1863 17.6975 18.1863 17.3025 17.9419 17.0581Z"
            fill="#33435B"
            fill-rule="evenodd"
            stroke="none"
          />
        </svg>
        <label
          class="mc-sr-only"
          for="searh-chat"
        >
          MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER
        </label>
        <input
          class="mc-rounded-6 mc-border-[1px] mc-border-grey-tint-80 mc-outline-none mc-py-8 mc-px-12 mc-w-full mc-h-40 mc-outline-none placeholder:mc-italic mc-placeholder-grey mc-bg-white-main focus:mc-border-bue-tint-40 mc-pl-32"
          data-testid="mc-input"
          id="searh-chat"
          placeholder="MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="mc-overflow-y-scroll mc-h-[calc(100%-55px)]"
    >
      <div
        data-testid="chat-wrapper-9efa85ea-f303-4e10-acb6-8aa68e244d36"
      >
        <div
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card-9efa85ea-f303-4e10-acb6-8aa68e244d36"
          tabindex="0"
        >
          <div
            class="mc-flex mc-flex-row mc-flex-wrap mc-items-start"
          >
            <span
              class="mc-mr-10"
            >
              O25-479880954498
            </span>
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  height="1.5rem"
                  stroke="none"
                  width="1.5rem"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g
                    clip-path="url(#a)"
                  >
                    <mask
                      height="24"
                      id="b"
                      maskUnits="userSpaceOnUse"
                      style="mask-type: alpha;"
                      width="24"
                      x="0"
                      y="0"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        fill="#fff"
                        r="12"
                      />
                    </mask>
                    <g
                      mask="url(#b)"
                    >
                      <path
                        d="M0 24h7.828l1.777-12.202L7.828 0H0v24Z"
                        fill="#6DA544"
                      />
                      <path
                        d="M24 0H7.828v24H24V0Z"
                        fill="#D80027"
                      />
                      <path
                        d="M7.828 16.172a4.172 4.172 0 1 0 0-8.344 4.172 4.172 0 0 0 0 8.344Z"
                        fill="#FFDA44"
                      />
                      <path
                        d="M5.48 9.914v2.602a2.346 2.346 0 0 0 4.692 0V9.909H5.484l-.004.005Z"
                        fill="#D80027"
                      />
                      <path
                        d="M7.828 13.303a.784.784 0 0 1-.783-.783v-1.036h1.566v1.032a.784.784 0 0 1-.783.782v.005Z"
                        fill="#EEE"
                      />
                    </g>
                  </g>
                  <defs>
                    <clippath
                      id="a"
                    >
                      <path
                        d="M0 0h24v24H0z"
                        fill="#fff"
                      />
                    </clippath>
                  </defs>
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                PT
              </span>
            </div>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15"
          >
            <p>
              Buyer: 
              Humpty
               
              Dumpty
            </p>
            <p>
              Seller: 
              My DESHOP
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              BUYER.INVOICES.INVOICE_CORRECTION
            </p>
          </div>
          <div
            class="mc-flex mc-justify-between mc-mt-16"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            >
              <div
                aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY"
                class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-orange-tint-80 mc-border-[1px] mc-border-metro-orange"
              >
                MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY
              </div>
            </div>
            <time
              class="leading-5 mc-font-normal mc-text-base"
              datetime="2025-07-08T18:52:12.000Z"
            >
              08.07.2025
            </time>
          </div>
        </div>
      </div>
      <div
        data-testid="chat-wrapper-new"
      >
        <div
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card-new"
          tabindex="0"
        >
          <div
            class="mc-flex mc-flex-row mc-flex-wrap mc-items-start"
          >
            <span
              class="mc-mr-10"
            >
              O25-473822061067
            </span>
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  height="1.5rem"
                  stroke="none"
                  width="1.5rem"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <defs>
                    <circle
                      cx="12"
                      cy="12"
                      id="a"
                      r="12"
                    />
                  </defs>
                  <g
                    fill="none"
                    fill-rule="evenodd"
                  >
                    <mask
                      fill="#fff"
                      id="b"
                    >
                      <use
                        xlink:href="#a"
                      />
                    </mask>
                    <use
                      fill="#D8D8D8"
                      xlink:href="#a"
                    />
                    <path
                      d="M0 0h24v24H0z"
                      fill="#FFDA43"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                    <path
                      d="M0 0h24v16H0z"
                      fill="#D80027"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                    <path
                      d="M0 0h24v8H0z"
                      fill="#000"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                  </g>
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                DE
              </span>
            </div>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15"
          >
            <p>
              Buyer: 
              Serghei
               
              Luchianenco
            </p>
            <p>
              Seller: 
              My DESHOP
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              SELLER.ORDERS.ORDER_CONFIRMATION
            </p>
          </div>
          <div
            class="mc-flex mc-justify-between mc-mt-16"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            />
            <time
              class="leading-5 mc-font-normal mc-text-base"
              datetime="2025-05-16T08:32:06.000Z"
            >
              16.05.2025
            </time>
          </div>
        </div>
      </div>
      <div
        data-testid="chat-wrapper-9eec7540-0b61-4b83-94d8-735739987e91"
      >
        <div
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card-9eec7540-0b61-4b83-94d8-735739987e91"
          tabindex="0"
        >
          <div
            class="mc-flex mc-flex-row mc-flex-wrap mc-items-start"
          >
            <span
              class="mc-mr-10"
            >
              O25-473820073331
            </span>
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  height="1.5rem"
                  stroke="none"
                  width="1.5rem"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <defs>
                    <circle
                      cx="12"
                      cy="12"
                      id="a"
                      r="12"
                    />
                  </defs>
                  <g
                    fill="none"
                    fill-rule="evenodd"
                  >
                    <mask
                      fill="#fff"
                      id="b"
                    >
                      <use
                        xlink:href="#a"
                      />
                    </mask>
                    <use
                      fill="#D8D8D8"
                      xlink:href="#a"
                    />
                    <path
                      d="M0 0h24v24H0z"
                      fill="#FFDA43"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                    <path
                      d="M0 0h24v16H0z"
                      fill="#D80027"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                    <path
                      d="M0 0h24v8H0z"
                      fill="#000"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                  </g>
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                DE
              </span>
            </div>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15"
          >
            <p>
              Buyer: 
              Roberto
               
              Alvarez
            </p>
            <p>
              Seller: 
              My DESHOP
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              BUYER.ORDERS.ORDER_TRACKING
            </p>
          </div>
          <div
            class="mc-flex mc-justify-between mc-mt-16"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            >
              <div
                aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.OVER_SLA"
                class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-red-tint-80 mc-border-[1px] mc-border-red-tint-80"
              >
                MESSAGE_CENTER.CHAT_STATUS.OVER_SLA
              </div>
            </div>
            <time
              class="leading-5 mc-font-normal mc-text-base"
              datetime="2025-05-16T08:29:26.000Z"
            >
              16.05.2025
            </time>
          </div>
        </div>
      </div>
      <div
        data-testid="chat-wrapper-9ef94b17-2ead-4824-8525-2e37f4080ca5"
      >
        <div
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card-9ef94b17-2ead-4824-8525-2e37f4080ca5"
          tabindex="0"
        >
          <div
            class="mc-flex mc-flex-row mc-flex-wrap mc-items-start"
          >
            <span
              class="mc-mr-10"
            >
              O25-476446721693
            </span>
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  height="1.5rem"
                  stroke="none"
                  width="1.5rem"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <defs>
                    <circle
                      cx="12"
                      cy="12"
                      id="a"
                      r="12"
                    />
                  </defs>
                  <g
                    fill="none"
                    fill-rule="evenodd"
                  >
                    <mask
                      fill="#fff"
                      id="b"
                    >
                      <use
                        xlink:href="#a"
                      />
                    </mask>
                    <use
                      fill="#D8D8D8"
                      xlink:href="#a"
                    />
                    <path
                      d="M0 0h24v24H0z"
                      fill="#FFDA43"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                    <path
                      d="M0 0h24v16H0z"
                      fill="#D80027"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                    <path
                      d="M0 0h24v8H0z"
                      fill="#000"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                  </g>
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                DE
              </span>
            </div>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15"
          >
            <p>
              Buyer: 
              John
               
              Doe
            </p>
            <p>
              Seller: 
              My DESHOP
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              BUYER.ORDERS.ORDER_TRACKING
            </p>
          </div>
          <div
            class="mc-flex mc-justify-between mc-mt-16"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            >
              <div
                aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY"
                class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-orange-tint-80 mc-border-[1px] mc-border-metro-orange"
              >
                MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY
              </div>
            </div>
            <time
              class="leading-5 mc-font-normal mc-text-base"
              datetime="2025-05-22T17:37:18.000Z"
            >
              22.05.2025
            </time>
          </div>
        </div>
      </div>
      <div
        data-testid="chat-wrapper-9efa92d2-6ec7-46ed-8c2b-b6a16596ed0b"
      >
        <div
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card-9efa92d2-6ec7-46ed-8c2b-b6a16596ed0b"
          tabindex="0"
        >
          <div
            class="mc-flex mc-flex-row mc-flex-wrap mc-items-start"
          >
            <span
              class="mc-mr-10"
            >
              O25-479889176657
            </span>
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  height="1.5rem"
                  stroke="none"
                  width="1.5rem"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g
                    clip-path="url(#a)"
                  >
                    <mask
                      height="24"
                      id="b"
                      maskUnits="userSpaceOnUse"
                      style="mask-type: alpha;"
                      width="24"
                      x="0"
                      y="0"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        fill="#fff"
                        r="12"
                      />
                    </mask>
                    <g
                      mask="url(#b)"
                    >
                      <path
                        d="M0 24h7.828l1.777-12.202L7.828 0H0v24Z"
                        fill="#6DA544"
                      />
                      <path
                        d="M24 0H7.828v24H24V0Z"
                        fill="#D80027"
                      />
                      <path
                        d="M7.828 16.172a4.172 4.172 0 1 0 0-8.344 4.172 4.172 0 0 0 0 8.344Z"
                        fill="#FFDA44"
                      />
                      <path
                        d="M5.48 9.914v2.602a2.346 2.346 0 0 0 4.692 0V9.909H5.484l-.004.005Z"
                        fill="#D80027"
                      />
                      <path
                        d="M7.828 13.303a.784.784 0 0 1-.783-.783v-1.036h1.566v1.032a.784.784 0 0 1-.783.782v.005Z"
                        fill="#EEE"
                      />
                    </g>
                  </g>
                  <defs>
                    <clippath
                      id="a"
                    >
                      <path
                        d="M0 0h24v24H0z"
                        fill="#fff"
                      />
                    </clippath>
                  </defs>
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                PT
              </span>
            </div>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15"
          >
            <p>
              Buyer: 
              Sam
               
              Taylor
            </p>
            <p>
              Seller: 
              My DESHOP
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              Blablabla
            </p>
          </div>
          <div
            class="mc-flex mc-justify-between mc-mt-16"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            >
              <div
                aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.OVER_SLA"
                class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-red-tint-80 mc-border-[1px] mc-border-red-tint-80"
              >
                MESSAGE_CENTER.CHAT_STATUS.OVER_SLA
              </div>
            </div>
            <time
              class="leading-5 mc-font-normal mc-text-base"
              datetime="2025-05-23T10:47:50.000Z"
            >
              23.05.2025
            </time>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
