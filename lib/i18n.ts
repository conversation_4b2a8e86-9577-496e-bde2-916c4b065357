import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import enTranslation from './translations/en.json'
import deTranslation from './translations/de.json'
import esTranslation from './translations/es.json'
import frTranslation from './translations/fr.json'
import itTranslation from './translations/it.json'
import nlTranslation from './translations/nl.json'
import ptTranslation from './translations/pt.json'

i18n.use(initReactI18next).init({
  resources: {
    en: { translation: enTranslation },
    de: { translation: deTranslation },
    es: { translation: esTranslation },
    fr: { translation: frTranslation },
    it: { translation: itTranslation },
    nl: { translation: nlTranslation },
    pt: { translation: ptTranslation },
  },
  lng: 'en',
  fallbackLng: 'en',
  interpolation: {
    escapeValue: false,
  },
})

export { i18n }
