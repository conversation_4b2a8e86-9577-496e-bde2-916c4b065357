import { DetailItem, AddressDisplay } from '@common/components'
import { Address, BuyerDetails } from '@common/types'
import { useTranslation } from 'react-i18next'
import { FC } from 'react'
import { CollapsiblePanel } from '@common/components/CollapsiblePanel/CollapsiblePanel'

interface Props {
  buyer?: BuyerDetails
  billingAddress?: Address
  shippingAddress?: Address
  isExpanded?: boolean
}

export const BuyerDetailsCard: FC<Props> = ({
  buyer,
  billingAddress,
  shippingAddress,
  isExpanded = false,
}) => {
  const { t } = useTranslation()

  if (!buyer) {
    return null
  }

  return (
    <CollapsiblePanel
      isExpanded={isExpanded}
      title={t('MESSAGE_CENTER.BUYER_DETAILS.CONTAINER_TITLE')}
    >
      <section>
        <DetailItem title={t('MESSAGE_CENTER.BUYER_DETAILS.FIRST_NAME')}>
          {buyer?.firstName}
        </DetailItem>
        <DetailItem title={t('MESSAGE_CENTER.BUYER_DETAILS.LAST_NAME')}>
          {buyer?.lastName}
        </DetailItem>
        <DetailItem title={t('MESSAGE_CENTER.BUYER_DETAILS.BILLING_ADDRESS')}>
          <AddressDisplay address={billingAddress} />
        </DetailItem>
        <DetailItem title={t('MESSAGE_CENTER.BUYER_DETAILS.SHIPPING_ADDRESS')}>
          <AddressDisplay address={shippingAddress} />
        </DetailItem>
      </section>
    </CollapsiblePanel>
  )
}
