import { render, screen } from '@testing-library/react'
import { BuyerDetailsCard } from '../BuyerDetailsCard'
import { BuyerDetails, Address } from '@common/types'

const buyerDetails: BuyerDetails = {
  firstName: '<PERSON>',
  lastName: 'Doe',
}

const billingAddress: Address = {
  firstName: 'Erika Billing',
  lastName: 'Mustermann',
  companyName: 'Beispiel GmbH Billing',
  street: 'Hauptstraße',
  houseNumberOrName: '101',
  postalCode: '10115',
  city: 'Berlin',
}

const shippingAddress: Address = {
  firstName: 'Erika Shipping',
  lastName: 'Mustermann',
  companyName: 'Beispiel GmbH Shipping',
  street: 'Nebenweg',
  houseNumberOrName: '5a',
  postalCode: '80331',
  city: 'München',
}

describe('BuyerDetailsCard', () => {
  it('renders buyer details and passes address props to child components', () => {
    render(
      <BuyerDetailsCard
        buyer={buyerDetails}
        billingAddress={billingAddress}
        shippingAddress={shippingAddress}
      />
    )

    expect(screen.getByText(buyerDetails.firstName)).toBeInTheDocument()
    expect(screen.getByText(buyerDetails.lastName)).toBeInTheDocument()

    const billingAddressContainer = screen.getByText(
      'MESSAGE_CENTER.BUYER_DETAILS.BILLING_ADDRESS'
    ).parentElement
    const shippingAddressContainer = screen.getByText(
      'MESSAGE_CENTER.BUYER_DETAILS.SHIPPING_ADDRESS'
    ).parentElement

    expect(billingAddressContainer).toHaveTextContent(
      billingAddress.companyName
    )
    expect(shippingAddressContainer).toHaveTextContent(
      shippingAddress.companyName
    )
  })

  it('renders nothing if buyerDetails is not provided', () => {
    const { container } = render(<BuyerDetailsCard />)
    expect(container).toBeEmptyDOMElement()
  })
})
