import { AppVariant, Chat } from '@common/types'
import { Container } from '@common/components'
import { ChatCard } from '@common/components/ChatCard/ChatCard'
import { NoSearchResult } from '@common/components/NoSearchResult/NoSearchResult'
import { SearchChat } from '@common/components/SearchChat/SearchChat'
import classNames from 'classnames'
import { FC, useEffect, useRef, useState } from 'react'
import { isElementInViewport } from '@common/utils/element-position.util'

interface Props {
  chats: Chat[]
  onSearch: (searchText: string) => void
  onSelectChat: (chat: Chat) => void
  selectedChat?: Chat
}

export const SellerChatList: FC<Props> = ({
  chats,
  onSelectChat,
  onSearch,
  selectedChat,
}) => {
  const [searchText, setSearchText] = useState('')
  const chatRefs = useRef<Record<string, HTMLDivElement | null>>({})

  const noSearchResult = searchText !== '' && chats.length === 0

  useEffect(() => {
    onSearch(searchText)
  }, [searchText])

  useEffect(() => {
    if (selectedChat?.id) {
      scrollToChat(selectedChat.id)
    }
  }, [selectedChat])

  const scrollToChat = (chatId: string) => {
    const el = chatRefs.current[chatId]
    if (el && !isElementInViewport(el, 'mc-overflow-y-scroll')) {
      el.scrollIntoView({ behavior: 'auto', block: 'start' })
    }
  }

  return (
    <Container>
      <div className="mc-sticky mc-top-[0px]">
        <SearchChat onChange={setSearchText} />
        {noSearchResult && <NoSearchResult searchText={searchText} />}
      </div>
      <div
        className={classNames('mc-overflow-y-scroll', {
          'mc-h-[calc(100%-55px)]': chats.length > 0,
          'mc-h-auto': chats.length === 0,
        })}
      >
        {chats.map((chat) => (
          <div
            key={chat.id}
            ref={(el) => {
              chatRefs.current[chat.id] = el
            }}
            data-testid={`chat-wrapper-${chat.id}`}
          >
            <ChatCard
              onClick={() => onSelectChat(chat)}
              chat={chat}
              variant={AppVariant.Seller}
            />
          </div>
        ))}
      </div>
    </Container>
  )
}
