import { render, screen } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { SellerChatList } from '../SellerChatList'
import { isElementInViewport } from '@common/utils/element-position.util'
import { mockChatList } from '../../../../common/mocks/chatlist'

vi.mock('@common/utils/element-position.util', () => ({
  isElementInViewport: vi.fn(),
}))

vi.mock('react-i18next', () => ({
  useTranslation: () => ({ t: (key: string) => key }),
}))

describe('SellerChatList', () => {
  const scrollIntoViewMock = vi.fn()
  beforeAll(() => {
    Element.prototype.scrollIntoView = vi.fn()
    HTMLElement.prototype.scrollIntoView = scrollIntoViewMock
  })
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders all chat items and matches snapshot', () => {
    const { container } = render(
      <SellerChatList
        chats={mockChatList}
        onSearch={() => {}}
        onSelectChat={() => {}}
      />
    )

    const chatCards = screen.getAllByTestId('chat-card')
    expect(chatCards.length).toBe(mockChatList.length)

    expect(container).toMatchSnapshot()
  })

  it('calls scrollIntoView when selected chat is not in viewport', () => {
    isElementInViewport.mockReturnValue(false)
    const selectedChat = mockChatList[mockChatList.length - 1]
    render(
      <SellerChatList
        chats={mockChatList}
        onSearch={() => {}}
        onSelectChat={() => {}}
        selectedChat={selectedChat}
      />
    )
    const chatElement = screen.getByTestId(`chat-wrapper-${selectedChat.id}`)
    expect(isElementInViewport).toHaveBeenCalledWith(
      chatElement,
      'mc-overflow-y-scroll'
    )
    expect(scrollIntoViewMock).toHaveBeenCalledWith({
      behavior: 'auto',
      block: 'start',
    })
  })

  it('does not call scrollIntoView if selected chat is already in viewport', () => {
    isElementInViewport.mockReturnValue(true)

    const selectedChat = mockChatList[1]
    const scrollIntoViewMock = vi.fn()
    const element = document.createElement('div')
    element.scrollIntoView = scrollIntoViewMock

    render(
      <SellerChatList
        chats={mockChatList}
        onSearch={() => {}}
        onSelectChat={() => {}}
        selectedChat={selectedChat}
      />
    )

    expect(scrollIntoViewMock).not.toHaveBeenCalled()
  })
})
