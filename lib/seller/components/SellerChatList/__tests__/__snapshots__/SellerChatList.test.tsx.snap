// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`SellerChatList > renders all chat items and matches snapshot 1`] = `
<div>
  <div
    class="mc-rounded-16 mc-bg-white-main mc-h-full mc-box-border mc-p-8 mc-font-lato mc-w-full"
  >
    <div
      class="mc-sticky mc-top-[0px]"
    >
      <div
        aria-label="MESSAGE_CENTER.CHATS.FILTERS.SEARCH.TEXT"
        class="mc-relative mc-overflow-hidden"
        role="search"
      >
        <svg
          aria-hidden="true"
          class="mc-block mc-align-middle mc-absolute mc-top-10 mc-left-10"
          data-testid="search-icon"
          fill=""
          height="1.25rem"
          viewBox="0 0 20 20"
          width="1.25rem"
          xmlns="http://www.w3.org/2000/svg"
          xmlns:xlink="http://www.w3.org/1999/xlink"
        >
          <path
            clip-rule="evenodd"
            d="M8.75 14.375C5.64813 14.375 3.125 11.8512 3.125 8.75C3.125 5.64875 5.64813 3.125 8.75 3.125C11.8519 3.125 14.375 5.64875 14.375 8.75C14.375 11.8512 11.8519 14.375 8.75 14.375ZM17.9419 17.0581L14.1919 13.3081C14.135 13.2512 14.0688 13.2106 13.9994 13.18C15.0113 11.9825 15.625 10.4375 15.625 8.75C15.625 4.95937 12.5406 1.875 8.75 1.875C4.95937 1.875 1.875 4.95937 1.875 8.75C1.875 12.5406 4.95937 15.625 8.75 15.625C10.4375 15.625 11.9825 15.0113 13.18 13.9994C13.2106 14.0688 13.2512 14.135 13.3081 14.1919L17.0581 17.9419C17.18 18.0638 17.34 18.125 17.5 18.125C17.66 18.125 17.82 18.0638 17.9419 17.9419C18.1863 17.6975 18.1863 17.3025 17.9419 17.0581Z"
            fill="#33435B"
            fill-rule="evenodd"
            stroke="none"
          />
        </svg>
        <label
          class="mc-sr-only"
          for="searh-chat"
        >
          MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER
        </label>
        <input
          class="mc-rounded-6 mc-border-[1px] mc-border-grey-tint-80 mc-outline-none mc-py-8 mc-px-12 mc-w-full mc-h-40 mc-outline-none placeholder:mc-italic mc-placeholder-grey mc-bg-white-main focus:mc-border-bue-tint-40 mc-pl-32"
          data-testid="mc-input"
          id="searh-chat"
          placeholder="MESSAGE_CENTER.CHAT.SEARCH.INPUT.PLACEHOLDER"
          type="text"
          value=""
        />
      </div>
    </div>
    <div
      class="mc-overflow-y-scroll mc-h-[calc(100%-55px)]"
    >
      <div
        data-testid="chat-wrapper-9ef94b17-2ead-4824-8525-2e37f4080ca5"
      >
        <button
          aria-describedby="chat-details-9ef94b17-2ead-4824-8525-2e37f4080ca5"
          aria-label="MESSAGE_CENTER.CHAT_WITH_SELLER"
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card"
          type="button"
        >
          <span
            class="mc-sr-only"
            id="chat-details-9ef94b17-2ead-4824-8525-2e37f4080ca5"
          >
            MESSAGE_CENTER.CHAT_READ
            .
            MESSAGE_CENTER.LAST_MESSAGE_SENT_ON Thursday, May 22, 2025 MESSAGE_CENTER.AT 5:37 PM
            .
            MESSAGE_CENTER.SUBJECT: MESSAGE_CENTER.BUYER.SUBJECT.ORDERS &gt; MESSAGE_CENTER.BUYER.SUBJECT.ORDER_TRACKING
          </span>
          <div
            class="mc-flex mc-justify-between mc-items-center"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            >
              <div
                aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY"
                class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-orange-tint-80 mc-border-[1px] mc-border-metro-orange"
              >
                MESSAGE_CENTER.CHAT_STATUS.NEEDS_REPLY
              </div>
            </div>
            <time
              class="leading-5 mc-font-normal mc-text-base"
              datetime="2025-05-22T17:37:18.000Z"
            >
              22.05.2025
            </time>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15 mc-font-normal"
          >
            <p
              class="mc-mt-4 mc-break-words"
            >
              John Doe
            </p>
            <p
              class="mc-mt-4"
            >
              O25-476446721693
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              MESSAGE_CENTER.BUYER.SUBJECT.ORDERS &gt; MESSAGE_CENTER.BUYER.SUBJECT.ORDER_TRACKING
            </p>
          </div>
          <div
            class="mc-mt-16"
          >
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  height="1.5rem"
                  stroke="none"
                  width="1.5rem"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                >
                  <defs>
                    <circle
                      cx="12"
                      cy="12"
                      id="a"
                      r="12"
                    />
                  </defs>
                  <g
                    fill="none"
                    fill-rule="evenodd"
                  >
                    <mask
                      fill="#fff"
                      id="b"
                    >
                      <use
                        xlink:href="#a"
                      />
                    </mask>
                    <use
                      fill="#D8D8D8"
                      xlink:href="#a"
                    />
                    <path
                      d="M0 0h24v24H0z"
                      fill="#FFDA43"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                    <path
                      d="M0 0h24v16H0z"
                      fill="#D80027"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                    <path
                      d="M0 0h24v8H0z"
                      fill="#000"
                      fill-rule="nonzero"
                      mask="url(#b)"
                    />
                  </g>
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                DE
              </span>
            </div>
          </div>
        </button>
      </div>
      <div
        data-testid="chat-wrapper-new"
      >
        <button
          aria-describedby="chat-details-new"
          aria-label="MESSAGE_CENTER.CHAT_WITH_SELLER"
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card"
          type="button"
        >
          <span
            class="mc-sr-only"
            id="chat-details-new"
          >
            MESSAGE_CENTER.CHAT_UNREAD
            .
            .
            MESSAGE_CENTER.SUBJECT: undefined &gt; Blablabla
          </span>
          <div
            class="mc-flex mc-justify-between mc-items-center"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            >
              <div
                aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.NEW"
                class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-grey-tint-80 mc-border-[1px] mc-border-grey-tint-50"
              >
                MESSAGE_CENTER.CHAT_STATUS.NEW
              </div>
              <div
                aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.OVER_SLA"
                class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-red-tint-80 mc-border-[1px] mc-border-red-tint-80"
              >
                MESSAGE_CENTER.CHAT_STATUS.OVER_SLA
              </div>
            </div>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15 mc-font-bold"
          >
            <p
              class="mc-mt-4 mc-break-words"
            >
              Sam Taylor
            </p>
            <p
              class="mc-mt-4"
            >
              O25-479889176657
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              undefined &gt; Blablabla
            </p>
          </div>
          <div
            class="mc-mt-16"
          >
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  height="1.5rem"
                  stroke="none"
                  width="1.5rem"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g
                    clip-path="url(#a)"
                  >
                    <mask
                      height="24"
                      id="b"
                      maskUnits="userSpaceOnUse"
                      style="mask-type: alpha;"
                      width="24"
                      x="0"
                      y="0"
                    >
                      <circle
                        cx="12"
                        cy="12"
                        fill="#fff"
                        r="12"
                      />
                    </mask>
                    <g
                      mask="url(#b)"
                    >
                      <path
                        d="M0 24h7.828l1.777-12.202L7.828 0H0v24Z"
                        fill="#6DA544"
                      />
                      <path
                        d="M24 0H7.828v24H24V0Z"
                        fill="#D80027"
                      />
                      <path
                        d="M7.828 16.172a4.172 4.172 0 1 0 0-8.344 4.172 4.172 0 0 0 0 8.344Z"
                        fill="#FFDA44"
                      />
                      <path
                        d="M5.48 9.914v2.602a2.346 2.346 0 0 0 4.692 0V9.909H5.484l-.004.005Z"
                        fill="#D80027"
                      />
                      <path
                        d="M7.828 13.303a.784.784 0 0 1-.783-.783v-1.036h1.566v1.032a.784.784 0 0 1-.783.782v.005Z"
                        fill="#EEE"
                      />
                    </g>
                  </g>
                  <defs>
                    <clippath
                      id="a"
                    >
                      <path
                        d="M0 0h24v24H0z"
                        fill="#fff"
                      />
                    </clippath>
                  </defs>
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                PT
              </span>
            </div>
          </div>
        </button>
      </div>
      <div
        data-testid="chat-wrapper-f3a9a88e-5b3c-4b1e-9f0a-7e8d6c5b4a32"
      >
        <button
          aria-describedby="chat-details-f3a9a88e-5b3c-4b1e-9f0a-7e8d6c5b4a32"
          aria-label="MESSAGE_CENTER.CHAT_WITH_SELLER"
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card"
          type="button"
        >
          <span
            class="mc-sr-only"
            id="chat-details-f3a9a88e-5b3c-4b1e-9f0a-7e8d6c5b4a32"
          >
            MESSAGE_CENTER.CHAT_UNREAD
            .
            MESSAGE_CENTER.LAST_MESSAGE_SENT_ON Wednesday, May 21, 2025 MESSAGE_CENTER.AT 11:20 AM
            .
            MESSAGE_CENTER.SUBJECT: undefined &gt; Needs reply but is also over SLA
          </span>
          <div
            class="mc-flex mc-justify-between mc-items-center"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            >
              <div
                aria-label="MESSAGE_CENTER.CHAT_STATUS MESSAGE_CENTER.CHAT_STATUS.OVER_SLA"
                class="mc-rounded-20 mc-text-metro-blue-shade-10 mc-px-10 mc-py-2 mc-bg-red-tint-80 mc-border-[1px] mc-border-red-tint-80"
              >
                MESSAGE_CENTER.CHAT_STATUS.OVER_SLA
              </div>
            </div>
            <time
              class="leading-5 mc-font-normal mc-text-base"
              datetime="2025-05-21T11:20:10.000Z"
            >
              21.05.2025
            </time>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15 mc-font-bold"
          >
            <p
              class="mc-mt-4 mc-break-words"
            >
              Jane Smith
            </p>
            <p
              class="mc-mt-4"
            >
              O25-112233445566
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              undefined &gt; Needs reply but is also over SLA
            </p>
          </div>
          <div
            class="mc-mt-16"
          >
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  stroke="none"
                  viewBox="0 0 512 512"
                  xml:space="preserve"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle
                    cx="256"
                    cy="256"
                    r="256"
                    style="fill: #f0f0f0;"
                  />
                  <path
                    d="M512 256c0-110.071-69.472-203.906-166.957-240.077v480.155C442.528 459.906 512 366.071 512 256z"
                    style="fill: #d80027;"
                  />
                  <path
                    d="M0 256c0 110.071 69.473 203.906 166.957 240.077V15.923C69.473 52.094 0 145.929 0 256z"
                    style="fill: #0052b4;"
                  />
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                FR
              </span>
            </div>
          </div>
        </button>
      </div>
      <div
        data-testid="chat-wrapper-b4c5d6e7-f8a9-b0c1-d2e3-f4a5b6c7d8e9"
      >
        <button
          aria-describedby="chat-details-b4c5d6e7-f8a9-b0c1-d2e3-f4a5b6c7d8e9"
          aria-label="MESSAGE_CENTER.CHAT_WITH_SELLER"
          class="mc-flex mc-flex-nowrap mc-flex-col mc-cursor-pointer mc-p-16 mc-text-grey hover:mc-bg-blue-tint-95 mc-border-b mc-border-b-grey-tint-80 mc-text-left mc-w-full"
          data-testid="chat-card"
          type="button"
        >
          <span
            class="mc-sr-only"
            id="chat-details-b4c5d6e7-f8a9-b0c1-d2e3-f4a5b6c7d8e9"
          >
            MESSAGE_CENTER.CHAT_READ
            .
            MESSAGE_CENTER.LAST_MESSAGE_SENT_ON Saturday, May 24, 2025 MESSAGE_CENTER.AT 9:00 AM
            .
            MESSAGE_CENTER.SUBJECT: undefined &gt; A regular chat, nothing special
          </span>
          <div
            class="mc-flex mc-justify-between mc-items-center"
          >
            <div
              class="mc-flex mc-items-center mc-gap-2"
            />
            <time
              class="leading-5 mc-font-normal mc-text-base"
              datetime="2025-05-24T09:00:00.000Z"
            >
              24.05.2025
            </time>
          </div>
          <div
            class="mc-leading-6 mc-text-regular margin mc-mt-15 mc-font-normal"
          >
            <p
              class="mc-mt-4 mc-break-words"
            >
              Alex Jones
            </p>
            <p
              class="mc-mt-4"
            >
              O25-998877665544
            </p>
            <p
              class="mc-mt-4 mc-break-words"
            >
              undefined &gt; A regular chat, nothing special
            </p>
          </div>
          <div
            class="mc-mt-16"
          >
            <div
              class="mc-rounded-20 mc-w-max mc-py-[1px] mc-px-5 mc-bg-blue-tint-70 mc-p mc-flex mc-items-center mc-flex-row mc-border-[1px] mc-border-secondary-blue-main"
            >
              <svg
                aria-hidden="true"
                class="mc-block mc-align-middle"
                data-testid="svg"
                fill=""
                height="1.125rem"
                viewBox="0 0 24 24"
                width="1.125rem"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <svg
                  fill="currentColor"
                  stroke="none"
                  viewBox="0 0 512 512"
                  xml:space="preserve"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M0 256c0 31.314 5.633 61.31 15.923 89.043L256 367.304l240.077-22.261C506.367 317.31 512 287.314 512 256s-5.633-61.31-15.923-89.043L256 144.696 15.923 166.957C5.633 194.69 0 224.686 0 256z"
                    style="fill: #ffda44;"
                  />
                  <path
                    d="M496.077 166.957C459.906 69.473 366.071 0 256 0S52.094 69.473 15.923 166.957h480.154zM15.923 345.043C52.094 442.527 145.929 512 256 512s203.906-69.473 240.077-166.957H15.923z"
                    style="fill: #d80027;"
                  />
                </svg>
              </svg>
              <span
                class="mc-text-base mc-ml-4"
              >
                ES
              </span>
            </div>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>
`;
