import { Chat } from '@common/types'
import { mockChatList } from '@common/mocks/chatlist'
import { mockOrders } from '@common/mocks/mockOrders'
import { mockEmployeeChatList } from '@common/mocks/employee-chatlist'

export const getChats = async (searchText: string): Promise<Chat[]> => {
  const chatList = mockChatList
  return new Promise((resolve) => {
    if (searchText) {
      resolve(chatList.filter((chat) => chat.order.orderNumber === searchText))
    } else {
      resolve(chatList)
    }
  })
}

export const getEmployeeChats = async (searchText: string): Promise<Chat[]> => {
  let chatList: Chat[] = mockEmployeeChatList

  return new Promise((resolve) => {
    if (searchText) {
      resolve(
        (chatList as Chat[]).filter(
          (chat) => chat.order.orderNumber === searchText
        )
      )
    } else {
      resolve(chatList)
    }
  })
}

export const getOrder = async (orderNumber: string) => {
  return new Promise((resolve) => {
    resolve(mockOrders.find((chat) => chat.orderNumber === orderNumber))
  })
}
