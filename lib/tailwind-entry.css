.mc {
  @tailwind base;
  @tailwind components;
  @tailwind utilities;
}

@keyframes mc-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes mc-dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}

.mc-animate-spin {
  animation: mc-spin 2s linear infinite;
}

.mc-animate-dash {
  animation: mc-dash 1.5s ease-in-out infinite;
}

@layer utilities {
  .mc-subject-active {
    background: var(
      --backgrounds-active,
      linear-gradient(
        0deg,
        rgba(255, 255, 255, 0.9) 0%,
        rgba(255, 255, 255, 0.9) 100%
      ),
      #0059e4
    );
  }
}

.mc-button-disabled {
  background: #001432 !important;
  color: #677283 !important;
}
