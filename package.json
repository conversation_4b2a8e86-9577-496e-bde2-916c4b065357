{"name": "@metromarkets/message-center-sdk", "private": false, "version": "1.12.2", "description": "A message center SDK", "type": "module", "main": "./dist/main.js", "types": "./dist/main.d.ts", "repository": {"type": "git", "url": "https://git.cps.metro-markets.org/mpdev/marketplace-libraries/message-center-sdk.git"}, "publishConfig": {"registry": "https://artifactory.cps-artifacts.metro-markets.org/artifactory/api/npm/npm-hosted/"}, "exports": {".": {"types": "./dist/main.d.ts", "default": "./dist/main.js"}, "./styles.css": {"default": "./dist/styles.css"}}, "sideEffects": ["dist/styles.css"], "files": ["dist"], "scripts": {"dev": "vite", "build:css": "tailwindcss -m -i ./lib/tailwind-entry.css -o ./dist/styles.css", "watch:css": "tailwindcss -m -i ./lib/tailwind-entry.css -o ./dist/styles.css --watch", "build": "tsc -b ./tsconfig.lib.json && vite build && pnpm build:css", "lint": "eslint .", "lint:fix": "eslint --fix .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "prepublish": "pnpm build", "publish:lib": "pnpm prepublish && pnpm publish --registry=https://artifactory.cps-artifacts.metro-markets.org/artifactory/api/npm/npm-virtual/", "publish:dev": "pnpm prepublish && pnpm publish --no-git-checks --registry=https://artifactory.cps-artifacts.metro-markets.org/artifactory/api/npm/npm-virtual/", "publish:ci": "npm publish --no-git-checks --registry=https://artifactory.cps-artifacts.metro-markets.org/artifactory/api/npm/npm-virtual/", "prepare": "husky", "test": "vitest run", "test:watch": "vitest watch", "test:coverage": "vitest run --coverage"}, "peerDependencies": {"axios": "^0.26.1", "react": "^18.3.1", "react-dom": "^18.3.1"}, "dependencies": {"autoprefixer": "^10.4.20", "axios": "^0.26.1", "classnames": "^2.5.1", "dayjs": "^1.11.13", "i18next": "^24.1.0", "postcss": "^8.4.49", "react": "^18.3.1", "react-dom": "^18.3.1", "react-i18next": "^15.1.4"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.0", "@eslint/js": "^9.15.0", "@storybook/addon-a11y": "^9.1.1", "@storybook/addon-essentials": "^8.6.14", "@storybook/react": "^9.1.1", "@storybook/react-vite": "^9.1.1", "@tailwindcss/typography": "^0.5.15", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.2.1", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.26.0", "@typescript-eslint/parser": "^8.26.0", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^2.1.9", "eslint": "^9.15.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-import-quotes": "^0.0.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "eslint-plugin-storybook": "^9.1.1", "eslint-plugin-testing-library": "^7.1.1", "glob": "^11.0.0", "globals": "^15.12.0", "husky": "^9.1.7", "jsdom": "^24.0.0", "postcss-loader": "^8.1.1", "prettier": "^3.4.2", "storybook": "^9.1.1", "storybook-react-i18next": "^4.0.11", "tailwindcss": "^3.4.16", "tailwindcss-hyphens": "^0.1.0", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1", "vite-plugin-dts": "^4.3.0", "vitest": "^2.1.9"}, "packageManager": "pnpm@10.6.2+sha512.47870716bea1572b53df34ad8647b42962bc790ce2bf4562ba0f643237d7302a3d6a8ecef9e4bdfc01d23af1969aa90485d4cebb0b9638fa5ef1daef656f6c1b"}