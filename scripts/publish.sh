#!/usr/bin/env bash

set -e  # Exit on any error

echo "Starting publish process..."

# Import the pre-configured JFrog CLI configuration
echo "Importing JFrog CLI configuration..."
jfrog config import "${MMA_ARTIFACTORY_CLI_CONFIG}"

# Activate the metro-markets configuration
echo "Activating metro-markets configuration..."
jfrog config use metro-markets

# Configure npm for Artifactory with the deployer repository
echo "Configuring npm for Artifactory..."
jfrog npm-config --repo-deploy=npm-hosted

# Check JFrog CLI configuration
echo "Checking JFrog CLI configuration..."
jfrog config show

# Test connection
echo "Testing connection to Artifactory..."
jfrog rt ping


# Publish using JFrog CLI
echo "Publishing package..."
jfrog npm publish