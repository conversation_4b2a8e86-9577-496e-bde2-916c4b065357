#!/usr/bin/env bash

# Import the pre-configured JFrog CLI configuration
jfrog config import "${MMA_ARTIFACTORY_CLI_CONFIG}"
# Activate the metro-markets configuration
jfrog config use metro-markets

# Configure npm for Artifactory. This creates a .npmrc file in the project root.
jfrog npm-config

# Check JFrog CLI configuration
jfrog config show

# Test connection
jfrog rt ping

# Check npm authentication
npm whoami 
jfrog npm publish