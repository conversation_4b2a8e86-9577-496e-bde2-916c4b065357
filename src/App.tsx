import { MessageCenterProvider } from '@common/contexts/MessageCenterProvider'
import {
  ChatInput,
  BuyerDetailsCard,
  ChatSubject,
  EmployeeChatList,
  OrderDetails,
  SellerChatList,
  BuyerChatList,
} from '../lib/main'
import { useRef, useState } from 'react'
import { AppVariant, Chat, ChatInputRef, Order } from '@common/types'
import {
  getChats,
  getEmployeeChats,
  getOrder,
} from '../lib/seller/services/SellerService'
import { subjects } from '@common/mocks/subjects'
import { MessageHistory } from '@common/components/MessageHistory/MessageHistory'
import { mockMessages } from '@common/mocks/messages'
import { mockEmployeeChatList } from '@common/mocks/employee-chatlist'

/**
 * Test application to demonstrate the usage of library
 */
function App() {
  const [chats, setChats] = useState<Chat[]>([])
  const [order, setOrder] = useState<Order>()
  const [employeeChats, setEmployeeChats] = useState<Chat[]>([])

  const fetchChatList = async (searchText: string) => {
    try {
      const chatsResponse = await getChats(searchText)
      setChats(chatsResponse)
    } catch {
      // TODO: Will be implemented later when we have BE
    } finally {
    }
  }

  const fetchEmployeeChatList = async (searchText: string) => {
    try {
      const chatsResponse = await getEmployeeChats(searchText)
      setEmployeeChats(chatsResponse)
    } catch {
      // TODO: Will be implemented later when we have BE
    } finally {
    }
  }

  const handleChatClick = async (selectedChat: Chat) => {
    if (!selectedChat.isSeen) {
      const updatedChat = { ...selectedChat, isSeen: true }
      setChats((prevChats) =>
        prevChats.map((chat) =>
          chat.id === selectedChat.id ? updatedChat : chat
        )
      )
    }
    setOrder((await getOrder(selectedChat.order.orderNumber)) as any)
  }

  const chatInputRef = useRef<ChatInputRef>(null)

  const settings = {
    appVariant: AppVariant.Employee,
    language: 'de',
  }

  const getSignedUrl = async (files: any) => {
    return Promise.resolve({
      data: files.map((x) => ({
        ...x,
        signedUrl: 'https://example.com/upload-url',
      })),
      status: 'success',
    })
  }

  return (
    <div className="mc">
      <MessageCenterProvider settings={settings} subjects={subjects}>
        <div className="mc">
          <div className="mc-flex mc-gap-24">
            <div className="mc-flex-1">
              {/*<EmployeeChatList*/}
              {/*  chats={employeeChats}*/}
              {/*  onSelectChat={handleChatClick}*/}
              {/*  onSearch={fetchEmployeeChatList}*/}
              {/*/>*/}
              {/*<BuyerChatList onSelectChat={() => {}} />*/}
            </div>
            {/* <div className="mc-flex-1">
              <SellerChatList
                chats={chats}
                onSelectChat={handleChatClick}
                onSearch={fetchChatList}
              />
              <BuyerChatList
                chats={chats}
                onSelectChat={() => {}}
                onSearch={fetchChatList}
              />
            </div> */}
            {/*<BuyerChatList*/}
            {/*  chats={chats}*/}
            {/*  onSelectChat={() => {}}*/}
            {/*  onSearch={fetchChatList}*/}
            {/*/>*/}
            <div
              className="mc-w-full mc-flex mc-flex-col"
              // style={{ height: '500px' }}
            >
              {/*<ChatSubject onSelectSubject={() => {}} />*/}
              {/* Wrap MessageHistory to allow it to take available space and ensure ChatInput is at bottom */}
              <div className="mc-flex-1 mc-min-h-0  mc-w-44">
                {/* mc-min-h-0 is crucial for flex item to shrink if content overflows */}
                {/*<MessageHistory*/}
                {/*  messages={mockMessages}*/}
                {/*  chat={mockEmployeeChatList[1]}*/}
                {/*/>*/}
              </div>
              <div className="u-margin__top--20 mc-w-44">
                <ChatInput
                  getSignedUrl={getSignedUrl}
                  // eslint-disable-next-line no-console
                  onSubmit={(e) => console.log(e)}
                  ref={chatInputRef}
                  disabled={false}
                  // eslint-disable-next-line no-console
                  onError={(x) => console.error(x)}
                />
              </div>
            </div>
            <div className="mc-flex-2">
              <div className="mc-flex-col">
                <OrderDetails order={order} />
                <BuyerDetailsCard {...order} />
              </div>
            </div>
          </div>
        </div>
      </MessageCenterProvider>
    </div>
  )
}

export default App
