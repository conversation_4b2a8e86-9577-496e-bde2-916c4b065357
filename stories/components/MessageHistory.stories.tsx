import type { Meta, StoryObj } from '@storybook/react-vite'
import { MessageHistory } from '../../lib/common/components/MessageHistory/MessageHistory'
import { mockStoryData } from '../mocks/messageHistoryMocks'

const meta: Meta<typeof MessageHistory> = {
  title: 'Components/MessageHistory',
  component: MessageHistory,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
The MessageHistory component displays a conversation between users with support for text messages and file attachments.

## Features
- Text message display with proper sender/receiver alignment
- File attachment support with download functionality
- Automatic scrolling to latest messages
- Responsive design for different screen sizes
- Internationalization support
- Accessibility features with proper ARIA labels

## Usage
The component requires messages array and chat object as props. Messages are displayed in chronological order with the most recent at the bottom.
        `,
      },
    },
  },
  argTypes: {
    messages: {
      control: 'object',
      description: 'Array of messages to display in the conversation',
    },
    chat: {
      control: 'object',
      description:
        'Chat object containing conversation metadata and participants',
    },
    children: {
      control: false,
      description:
        'Optional children to render above the message history header',
    },
  },
  decorators: [
    (Story) => (
      <div style={{ height: '600px', width: '100%', display: 'flex' }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof MessageHistory>

/**
 * Default story showing a basic conversation with text messages only.
 * This demonstrates the fundamental message display functionality with
 * proper sender/receiver alignment and timestamp formatting.
 */
export const Default: Story = {
  args: {
    messages: mockStoryData.basic.messages,
    chat: mockStoryData.basic.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Basic conversation showing text-only messages between a buyer and seller.
This story demonstrates:
- Message alignment (seller messages on left, buyer messages on right)
- Timestamp display
- Message content rendering
- Conversation flow
        `,
      },
    },
  },
}

/**
 * Story showing messages with single file attachments.
 * Demonstrates how the component handles different file types
 * including PDFs, images, and documents.
 */
export const WithAttachments: Story = {
  args: {
    messages: mockStoryData.singleAttachment.messages,
    chat: mockStoryData.singleAttachment.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Messages containing single file attachments of different types.
This story demonstrates:
- PDF attachment display
- Image attachment display
- Download link functionality
- File type icon rendering
- Attachment metadata (filename, size)
        `,
      },
    },
  },
}

/**
 * Story showing messages with multiple file attachments.
 * Demonstrates how the component handles messages containing
 * several files at once.
 */
export const WithMultipleAttachments: Story = {
  args: {
    messages: mockStoryData.multipleAttachments.messages,
    chat: mockStoryData.multipleAttachments.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Messages containing multiple file attachments in a single message.
This story demonstrates:
- Multiple attachment layout
- Different file types in one message
- Proper spacing between attachments
- Bulk attachment handling
        `,
      },
    },
  },
}

/**
 * Story showing a mix of text messages and messages with attachments.
 * This represents a typical real-world conversation flow where
 * some messages contain only text while others include files.
 */
export const MixedContent: Story = {
  args: {
    messages: mockStoryData.mixedContent.messages,
    chat: mockStoryData.mixedContent.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Realistic conversation mixing text-only messages with attachment messages.
This story demonstrates:
- Natural conversation flow
- Seamless transition between text and attachment messages
- Proper message spacing and alignment
- Real-world usage patterns
        `,
      },
    },
  },
}

/**
 * Story showing a long conversation to test scrolling behavior.
 * This demonstrates how the component handles many messages
 * and automatic scrolling to the latest message.
 */
export const LongConversation: Story = {
  args: {
    messages: mockStoryData.longConversation.messages,
    chat: mockStoryData.longConversation.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Extended conversation with many messages to test scrolling behavior.
This story demonstrates:
- Automatic scroll to latest message
- Performance with many messages
- Proper message ordering
- Scroll container behavior
        `,
      },
    },
  },
}

/**
 * Story showing messages with very long filenames to test
 * filename truncation and layout handling.
 */
export const LongFilename: Story = {
  args: {
    messages: mockStoryData.longFilename.messages,
    chat: mockStoryData.longFilename.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Messages with attachments having very long filenames.
This story demonstrates:
- Filename truncation behavior
- Layout preservation with long text
- Tooltip or full filename display
- Responsive attachment display
        `,
      },
    },
  },
}

/**
 * Story showing messages from different user types (BUYER, SELLER, EMPLOYEE).
 * This demonstrates how the component handles different sender types
 * and their visual representation.
 */
export const SenderReceiverMix: Story = {
  args: {
    messages: mockStoryData.senderReceiverMix.messages,
    chat: mockStoryData.senderReceiverMix.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Conversation involving different user types including employee intervention.
This story demonstrates:
- Different user type styling
- Employee message appearance
- Multi-party conversation handling
- User identification in messages
        `,
      },
    },
  },
}

/**
 * Story showing an empty message history.
 * This demonstrates the component's behavior when no messages are present.
 */
export const EmptyState: Story = {
  args: {
    messages: mockStoryData.emptyState.messages,
    chat: mockStoryData.emptyState.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Empty message history showing the component with no messages.
This story demonstrates:
- Empty state handling
- Component structure without content
- Header display with no messages
- Proper container sizing
        `,
      },
    },
  },
}

/**
 * Story showing a single message conversation.
 * This demonstrates the minimal case with just one message.
 */
export const SingleMessage: Story = {
  args: {
    messages: mockStoryData.singleMessage.messages,
    chat: mockStoryData.singleMessage.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Conversation with only a single message.
This story demonstrates:
- Minimal message display
- Single message layout
- Component behavior with minimal content
- Proper spacing with one message
        `,
      },
    },
  },
}

/**
 * Interactive story with controls for testing different scenarios.
 * This allows developers to experiment with different message
 * and chat configurations.
 */
export const Interactive: Story = {
  args: {
    messages: mockStoryData.basic.messages,
    chat: mockStoryData.basic.chat,
  },
  parameters: {
    docs: {
      description: {
        story: `
Interactive story with controls to test different message and chat configurations.
Use the controls panel to modify messages and chat properties to test various scenarios.
        `,
      },
    },
  },
}
