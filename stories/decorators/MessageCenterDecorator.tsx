import React from 'react'
import type { Decorator } from '@storybook/react'
import { MessageCenterProvider } from '../../lib/common/contexts/MessageCenterProvider'
import { subjects } from '../../lib/common/mocks/subjects'
import { AppVariant } from '../../lib/common/types/enums'
import { Settings } from '../../lib/common/types'
import i18n from '../../.storybook/i18next'

/**
 * MessageCenterDecorator provides the MessageCenterProvider context to all stories
 * with proper internationalization support and mock data integration.
 */
export const MessageCenterDecorator: Decorator = (Story, context) => {
  // Get the current locale from Storybook's global state
  const locale = context.globals.locale || 'en'

  // Update i18n language when locale changes
  React.useEffect(() => {
    if (i18n.language !== locale) {
      i18n.changeLanguage(locale)
    }
  }, [locale])

  // Create settings object with the current locale
  const settings: Partial<Settings> = {
    language: locale,
    appVariant: AppVariant.Employee, // Default to Employee variant for stories
    maxFileSize: '10MB',
    maxFiles: 2,
    supportedFormats: [
      'pdf',
      'png',
      'jpg',
      'jpeg',
      'xls',
      'doc',
      'docx',
      'txt',
    ],
  }

  return (
    <MessageCenterProvider settings={settings} subjects={subjects}>
      <Story />
    </MessageCenterProvider>
  )
}
