import { Message, Chat, Attachment } from '../../lib/common/types'

// Mock attachments for different scenarios
export const mockAttachments = {
  singlePdf: {
    fileId: 'file-pdf-001',
    fileName: 'invoice.pdf',
    fileType: 'application/pdf',
    fileSize: 245760,
    downloadUrl: 'https://example.com/download/invoice.pdf',
  } as Attachment,

  singleImage: {
    fileId: 'file-img-001',
    fileName: 'receipt.jpg',
    fileType: 'image/jpeg',
    fileSize: 1024000,
    downloadUrl: 'https://example.com/download/receipt.jpg',
  } as Attachment,

  singleDoc: {
    fileId: 'file-doc-001',
    fileName: 'contract.docx',
    fileType:
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    fileSize: 512000,
    downloadUrl: 'https://example.com/download/contract.docx',
  } as Attachment,

  longFilename: {
    fileId: 'file-long-001',
    fileName:
      'very-long-filename-that-should-be-truncated-in-the-ui-component-to-prevent-layout-issues-and-maintain-proper-spacing-throughout-the-message-history-display.pdf',
    fileType: 'application/pdf',
    fileSize: 2048000,
    downloadUrl: 'https://example.com/download/very-long-filename.pdf',
  } as Attachment,

  multipleFiles: [
    {
      fileId: 'file-multi-001',
      fileName: 'document1.pdf',
      fileType: 'application/pdf',
      fileSize: 345600,
      downloadUrl: 'https://example.com/download/document1.pdf',
    },
    {
      fileId: 'file-multi-002',
      fileName: 'image1.png',
      fileType: 'image/png',
      fileSize: 789000,
      downloadUrl: 'https://example.com/download/image1.png',
    },
    {
      fileId: 'file-multi-003',
      fileName: 'spreadsheet.xlsx',
      fileType:
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      fileSize: 456700,
      downloadUrl: 'https://example.com/download/spreadsheet.xlsx',
    },
  ] as Attachment[],
}

// Mock message data for different scenarios
export const mockMessageHistoryData = {
  // Basic conversation with text messages only
  basicConversation: [
    {
      id: 'msg-basic-001',
      chatId: 'chat-basic-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content: 'Hello! Thank you for contacting us. How can I help you today?',
      seenAt: null,
      createdAt: '2025-01-08T10:00:00Z',
      attachments: [],
    },
    {
      id: 'msg-basic-002',
      chatId: 'chat-basic-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        'Hi! I have a question about my recent order. The delivery date seems to have changed.',
      seenAt: null,
      createdAt: '2025-01-08T10:05:00Z',
      attachments: [],
    },
    {
      id: 'msg-basic-003',
      chatId: 'chat-basic-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'I understand your concern. Let me check the status of your order right away.',
      seenAt: null,
      createdAt: '2025-01-08T10:07:00Z',
      attachments: [],
    },
    {
      id: 'msg-basic-004',
      chatId: 'chat-basic-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'I can see that there was a slight delay due to weather conditions, but your order is now on track for delivery tomorrow.',
      seenAt: null,
      createdAt: '2025-01-08T10:10:00Z',
      attachments: [],
    },
    {
      id: 'msg-basic-005',
      chatId: 'chat-basic-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        "That's great news! Thank you for the quick response and update.",
      seenAt: '2025-01-08T10:15:00Z',
      createdAt: '2025-01-08T10:12:00Z',
      attachments: [],
    },
  ] as Message[],

  // Messages with single attachments
  withSingleAttachments: [
    {
      id: 'msg-single-001',
      chatId: 'chat-single-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        'I need to return this item. Please find the attached invoice for reference.',
      seenAt: null,
      createdAt: '2025-01-08T11:00:00Z',
      attachments: [mockAttachments.singlePdf],
    },
    {
      id: 'msg-single-002',
      chatId: 'chat-single-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'Thank you for providing the invoice. I can process your return request.',
      seenAt: null,
      createdAt: '2025-01-08T11:05:00Z',
      attachments: [],
    },
    {
      id: 'msg-single-003',
      chatId: 'chat-single-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'Here is your return label. Please print and attach it to the package.',
      seenAt: null,
      createdAt: '2025-01-08T11:10:00Z',
      attachments: [mockAttachments.singleImage],
    },
  ] as Message[],

  // Messages with multiple attachments
  withMultipleAttachments: [
    {
      id: 'msg-multi-001',
      chatId: 'chat-multi-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        'I have some concerns about the product quality. Please see the attached documents and photos.',
      seenAt: null,
      createdAt: '2025-01-08T12:00:00Z',
      attachments: mockAttachments.multipleFiles,
    },
    {
      id: 'msg-multi-002',
      chatId: 'chat-multi-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'Thank you for the detailed documentation. I will review all the files and get back to you within 24 hours.',
      seenAt: null,
      createdAt: '2025-01-08T12:15:00Z',
      attachments: [],
    },
  ] as Message[],

  // Mixed content with text and attachments
  mixedContent: [
    {
      id: 'msg-mixed-001',
      chatId: 'chat-mixed-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        "Welcome to our store! We're excited to help you with your order.",
      seenAt: null,
      createdAt: '2025-01-08T13:00:00Z',
      attachments: [],
    },
    {
      id: 'msg-mixed-002',
      chatId: 'chat-mixed-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content: 'Thank you! I have a question about the warranty terms.',
      seenAt: null,
      createdAt: '2025-01-08T13:05:00Z',
      attachments: [],
    },
    {
      id: 'msg-mixed-003',
      chatId: 'chat-mixed-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'Here are the complete warranty terms and conditions for your product.',
      seenAt: null,
      createdAt: '2025-01-08T13:10:00Z',
      attachments: [mockAttachments.singleDoc],
    },
    {
      id: 'msg-mixed-004',
      chatId: 'chat-mixed-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        'Perfect! This answers all my questions. The coverage looks comprehensive.',
      seenAt: null,
      createdAt: '2025-01-08T13:20:00Z',
      attachments: [],
    },
    {
      id: 'msg-mixed-005',
      chatId: 'chat-mixed-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        "Just to be safe, I'm also attaching a photo of the product serial number.",
      seenAt: null,
      createdAt: '2025-01-08T13:25:00Z',
      attachments: [mockAttachments.singleImage],
    },
  ] as Message[],

  // Long conversation for scrolling behavior testing
  longConversation: [
    {
      id: 'msg-long-001',
      chatId: 'chat-long-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        "Hi, I placed an order last week and I'm wondering about the delivery status.",
      seenAt: null,
      createdAt: '2025-01-08T09:00:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-002',
      chatId: 'chat-long-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        "Hello! I'd be happy to help you track your order. Could you please provide your order number?",
      seenAt: null,
      createdAt: '2025-01-08T09:15:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-003',
      chatId: 'chat-long-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content: 'Sure! My order number is O25-*********.',
      seenAt: null,
      createdAt: '2025-01-08T09:20:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-004',
      chatId: 'chat-long-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content: 'Thank you! Let me look that up for you right away.',
      seenAt: null,
      createdAt: '2025-01-08T09:22:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-005',
      chatId: 'chat-long-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'I can see your order is currently being prepared for shipment. It should be dispatched within the next 24 hours.',
      seenAt: null,
      createdAt: '2025-01-08T09:25:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-006',
      chatId: 'chat-long-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content: "That's great! Will I receive a tracking number once it ships?",
      seenAt: null,
      createdAt: '2025-01-08T09:30:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-007',
      chatId: 'chat-long-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        "Absolutely! You'll receive an email with the tracking information as soon as the package is picked up by our courier.",
      seenAt: null,
      createdAt: '2025-01-08T09:35:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-008',
      chatId: 'chat-long-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        'Perfect! One more question - is it possible to change the delivery address?',
      seenAt: null,
      createdAt: '2025-01-08T09:40:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-009',
      chatId: 'chat-long-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        "Since the order hasn't shipped yet, we might be able to update the address. What's the new address you'd like to use?",
      seenAt: null,
      createdAt: '2025-01-08T09:45:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-010',
      chatId: 'chat-long-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        "I'd like to change it to: 123 New Street, Apartment 4B, New City, 12345",
      seenAt: null,
      createdAt: '2025-01-08T09:50:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-011',
      chatId: 'chat-long-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        "I've updated your delivery address successfully. The order will be shipped to the new address.",
      seenAt: null,
      createdAt: '2025-01-08T09:55:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-012',
      chatId: 'chat-long-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        "Excellent! Thank you so much for your help. You've been very responsive.",
      seenAt: null,
      createdAt: '2025-01-08T10:00:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-013',
      chatId: 'chat-long-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        "You're very welcome! Is there anything else I can help you with today?",
      seenAt: null,
      createdAt: '2025-01-08T10:05:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-014',
      chatId: 'chat-long-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        'No, that covers everything. I really appreciate the excellent customer service!',
      seenAt: null,
      createdAt: '2025-01-08T10:10:00Z',
      attachments: [],
    },
    {
      id: 'msg-long-015',
      chatId: 'chat-long-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        "Thank you for the kind words! We're always here to help. Have a great day!",
      seenAt: '2025-01-08T10:20:00Z',
      createdAt: '2025-01-08T10:15:00Z',
      attachments: [],
    },
  ] as Message[],

  // Messages with long filename attachments
  longFilename: [
    {
      id: 'msg-longfile-001',
      chatId: 'chat-longfile-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'seller-001',
      content:
        'I need to submit this document for my warranty claim. The filename is quite long as it contains all the necessary details.',
      seenAt: null,
      createdAt: '2025-01-08T14:00:00Z',
      attachments: [mockAttachments.longFilename],
    },
    {
      id: 'msg-longfile-002',
      chatId: 'chat-longfile-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'Thank you for the document. I can see the file has been uploaded successfully despite the long filename.',
      seenAt: null,
      createdAt: '2025-01-08T14:10:00Z',
      attachments: [],
    },
  ] as Message[],

  // Sender/Receiver mix showing different user types
  senderReceiverMix: [
    {
      id: 'msg-mix-001',
      chatId: 'chat-mix-001',
      senderId: 'employee-001',
      senderUserType: 'EMPLOYEE',
      receiverId: 'buyer-001',
      content:
        "Hello! I'm from customer support. I see you've been having some issues with your recent order.",
      seenAt: null,
      createdAt: '2025-01-08T15:00:00Z',
      attachments: [],
    },
    {
      id: 'msg-mix-002',
      chatId: 'chat-mix-001',
      senderId: 'buyer-001',
      senderUserType: 'BUYER',
      receiverId: 'employee-001',
      content:
        "Yes, that's correct. The seller hasn't been responding to my messages.",
      seenAt: null,
      createdAt: '2025-01-08T15:05:00Z',
      attachments: [],
    },
    {
      id: 'msg-mix-003',
      chatId: 'chat-mix-001',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content:
        'I apologize for the delay. I was dealing with a family emergency. Let me address your concerns now.',
      seenAt: null,
      createdAt: '2025-01-08T15:10:00Z',
      attachments: [],
    },
    {
      id: 'msg-mix-004',
      chatId: 'chat-mix-001',
      senderId: 'employee-001',
      senderUserType: 'EMPLOYEE',
      receiverId: 'buyer-001',
      content:
        "Thank you for joining the conversation. Let's work together to resolve this issue.",
      seenAt: null,
      createdAt: '2025-01-08T15:15:00Z',
      attachments: [],
    },
  ] as Message[],

  // Empty state - no messages
  emptyState: [] as Message[],

  // Single message for minimal testing
  singleMessage: [
    {
      id: 'msg-single-only',
      chatId: 'chat-single-only',
      senderId: 'seller-001',
      senderUserType: 'SELLER',
      receiverId: 'buyer-001',
      content: 'This is the only message in this conversation.',
      seenAt: null,
      createdAt: '2025-01-08T16:00:00Z',
      attachments: [],
    },
  ] as Message[],
}

// Mock chat data with proper order information
export const mockChatData = {
  basicChat: {
    id: 'chat-basic-001',
    subject: 'BUYER.ORDERS.ORDER_TRACKING',
    customSubject: null,
    initiatorUserType: 'BUYER' as const,
    lastMessageAt: '2025-01-08T10:12:00Z',
    lastMessageByUserType: 'BUYER' as const,
    needsReply: false,
    overSLA: false,
    inactive: false,
    isSeen: true,
    createdAt: '2025-01-08T10:00:00Z',
    buyer: {
      id: 'buyer-001',
      firstName: 'John',
      lastName: 'Doe',
    },
    seller: {
      organization: {
        id: 'org-001',
        shopName: 'Tech Store Pro',
        name: 'Tech Store Pro Ltd.',
        userFriendlyId: 'techstore',
      },
      accounts: [
        {
          id: 'seller-001',
          firstName: 'Sarah',
          lastName: 'Johnson',
        },
      ],
    },
    order: {
      id: 'order-001',
      orderNumber: 'O25-*********',
      salesChannel: 'de' as const,
    },
  } as Chat,

  attachmentChat: {
    id: 'chat-attachment-001',
    subject: 'BUYER.RETURNS.RETURN_REQUEST',
    customSubject: 'Product Return Request',
    initiatorUserType: 'BUYER' as const,
    lastMessageAt: '2025-01-08T11:10:00Z',
    lastMessageByUserType: 'SELLER' as const,
    needsReply: true,
    overSLA: false,
    inactive: false,
    isSeen: false,
    createdAt: '2025-01-08T11:00:00Z',
    buyer: {
      id: 'buyer-001',
      firstName: 'Jane',
      lastName: 'Smith',
    },
    seller: {
      organization: {
        id: 'org-002',
        shopName: 'Electronics Hub',
        name: 'Electronics Hub Inc.',
        userFriendlyId: 'electronichub',
      },
      accounts: [
        {
          id: 'seller-002',
          firstName: 'Mike',
          lastName: 'Wilson',
        },
      ],
    },
    order: {
      id: 'order-002',
      orderNumber: 'O25-*********',
      salesChannel: 'fr' as const,
    },
  } as Chat,

  longConversationChat: {
    id: 'chat-long-001',
    subject: 'BUYER.ORDERS.ORDER_MODIFICATION',
    customSubject: 'Address Change Request',
    initiatorUserType: 'BUYER' as const,
    lastMessageAt: '2025-01-08T10:15:00Z',
    lastMessageByUserType: 'SELLER' as const,
    needsReply: false,
    overSLA: false,
    inactive: false,
    isSeen: true,
    createdAt: '2025-01-08T09:00:00Z',
    buyer: {
      id: 'buyer-003',
      firstName: 'Alex',
      lastName: 'Brown',
    },
    seller: {
      organization: {
        id: 'org-003',
        shopName: 'Home & Garden',
        name: 'Home & Garden Solutions',
        userFriendlyId: 'homegarden',
      },
      accounts: [
        {
          id: 'seller-003',
          firstName: 'Lisa',
          lastName: 'Davis',
        },
      ],
    },
    order: {
      id: 'order-003',
      orderNumber: 'O25-*********',
      salesChannel: 'es' as const,
    },
  } as Chat,

  mixedUserChat: {
    id: 'chat-mix-001',
    subject: 'BUYER.COMPLAINTS.SELLER_ISSUE',
    customSubject: 'Seller Response Issue',
    initiatorUserType: 'BUYER' as const,
    lastMessageAt: '2025-01-08T15:15:00Z',
    lastMessageByUserType: 'EMPLOYEE' as const,
    needsReply: true,
    overSLA: true,
    inactive: false,
    isSeen: false,
    createdAt: '2025-01-08T15:00:00Z',
    buyer: {
      id: 'buyer-004',
      firstName: 'Emma',
      lastName: 'Taylor',
    },
    seller: {
      organization: {
        id: 'org-004',
        shopName: 'Fashion Forward',
        name: 'Fashion Forward Boutique',
        userFriendlyId: 'fashionforward',
      },
      accounts: [
        {
          id: 'seller-004',
          firstName: 'David',
          lastName: 'Miller',
        },
      ],
    },
    order: {
      id: 'order-004',
      orderNumber: 'O25-*********',
      salesChannel: 'it' as const,
    },
  } as Chat,

  emptyChat: {
    id: 'chat-empty-001',
    subject: 'BUYER.OTHERS',
    customSubject: 'General Inquiry',
    initiatorUserType: 'BUYER' as const,
    lastMessageAt: null,
    lastMessageByUserType: null,
    needsReply: false,
    overSLA: false,
    inactive: false,
    isSeen: true,
    createdAt: '2025-01-08T16:00:00Z',
    buyer: {
      id: 'buyer-005',
      firstName: 'Chris',
      lastName: 'Anderson',
    },
    seller: {
      organization: {
        id: 'org-005',
        shopName: 'Sports Central',
        name: 'Sports Central Ltd.',
        userFriendlyId: 'sportscentral',
      },
      accounts: [
        {
          id: 'seller-005',
          firstName: 'Rachel',
          lastName: 'Green',
        },
      ],
    },
    order: {
      id: 'order-005',
      orderNumber: 'O25-*********',
      salesChannel: 'nl' as const,
    },
  } as Chat,
}

// Combined mock data for easy story creation
export const mockStoryData = {
  // Basic conversation scenario
  basic: {
    messages: mockMessageHistoryData.basicConversation,
    chat: mockChatData.basicChat,
  },

  // Single attachment scenario
  singleAttachment: {
    messages: mockMessageHistoryData.withSingleAttachments,
    chat: mockChatData.attachmentChat,
  },

  // Multiple attachments scenario
  multipleAttachments: {
    messages: mockMessageHistoryData.withMultipleAttachments,
    chat: mockChatData.attachmentChat,
  },

  // Mixed content scenario
  mixedContent: {
    messages: mockMessageHistoryData.mixedContent,
    chat: mockChatData.basicChat,
  },

  // Long conversation scenario
  longConversation: {
    messages: mockMessageHistoryData.longConversation,
    chat: mockChatData.longConversationChat,
  },

  // Long filename scenario
  longFilename: {
    messages: mockMessageHistoryData.longFilename,
    chat: mockChatData.attachmentChat,
  },

  // Sender/receiver mix scenario
  senderReceiverMix: {
    messages: mockMessageHistoryData.senderReceiverMix,
    chat: mockChatData.mixedUserChat,
  },

  // Empty state scenario
  emptyState: {
    messages: mockMessageHistoryData.emptyState,
    chat: mockChatData.emptyChat,
  },

  // Single message scenario
  singleMessage: {
    messages: mockMessageHistoryData.singleMessage,
    chat: mockChatData.basicChat,
  },
}
