const colors = require('./styles/colors')
const spacing = require('./styles/spacing')
const fontSize = require('./styles/font-size')

module.exports = {
  important: '.mc',
  prefix: 'mc-',
  content: ['./lib/**/*.{js,jsx,ts,tsx,css}'],
  theme: {
    screens: {
      xs: '375px',
      md: '768px',
      lg: '1024px',
      xl: '1440px',
    },
    extend: {
      fontFamily: {
        lato: ['Lato'],
      },
      borderRadius: spacing,
      colors,
      spacing,
      fontSize,
      maxWidth: {
        xs: '375px',
        md: '768px',
        lg: '1024px',
        xl: '1440px',
      },
      height: {
        '40px': '40px',
        '250px': '250px',
      },
      boxShadow: {
        searchSubjectResult: '0px 4px 16px 0px rgba(0, 20, 50, 0.20)',
        attachmentContainer: '0px 1px 3px 0px rgba(0, 20, 50, 0.20);',
      },
      zIndex: {
        5: '5',
      },
      backgroundImage: {
        'btn-disabled-diagonal-lines':
          'repeating-linear-gradient(45deg, #E6E8EB 0px, #E6E8EB 1.5px, transparent 2px, transparent 7px)',
      },
    },
  },
  plugins: [require('@tailwindcss/typography'), require('tailwindcss-hyphens')],
}
